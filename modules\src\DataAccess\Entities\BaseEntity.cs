// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.ComponentModel.DataAnnotations;

namespace ArtDesignFramework.DataAccess.Entities;

/// <summary>
/// Base entity class providing common audit fields and functionality
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// Gets or sets the unique identifier for the entity
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Gets or sets when the entity was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the entity was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets whether the entity is active (soft delete support)
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Gets or sets additional tags for categorization and filtering
    /// </summary>
    [MaxLength(1000)]
    public string? Tags { get; set; }

    /// <summary>
    /// Gets or sets additional notes or comments
    /// </summary>
    [<PERSON><PERSON>ength(2000)]
    public string? Notes { get; set; }
}

/// <summary>
/// Interface for entities that support soft delete
/// </summary>
public interface ISoftDeletable
{
    /// <summary>
    /// Gets or sets whether the entity is deleted
    /// </summary>
    bool IsDeleted { get; set; }

    /// <summary>
    /// Gets or sets when the entity was deleted
    /// </summary>
    DateTime? DeletedAt { get; set; }

    /// <summary>
    /// Gets or sets who deleted the entity
    /// </summary>
    string? DeletedBy { get; set; }
}

/// <summary>
/// Interface for entities that track who created and modified them
/// </summary>
public interface IAuditable
{
    /// <summary>
    /// Gets or sets who created the entity
    /// </summary>
    string? CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets who last modified the entity
    /// </summary>
    string? ModifiedBy { get; set; }
}

/// <summary>
/// Interface for entities that have versioning support
/// </summary>
public interface IVersionable
{
    /// <summary>
    /// Gets or sets the entity version for optimistic concurrency
    /// </summary>
    byte[] RowVersion { get; set; }
}

/// <summary>
/// Interface for entities that belong to a specific tenant
/// </summary>
public interface ITenantAware
{
    /// <summary>
    /// Gets or sets the tenant identifier
    /// </summary>
    string TenantId { get; set; }
}
