<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- AI Lighting module specific settings -->
    <AssemblyTitle>ArtDesignFramework AI Lighting Module</AssemblyTitle>
    <AssemblyDescription>AI-powered lighting and shadow generation for the ArtDesignFramework</AssemblyDescription>
  </PropertyGroup>

  <ItemGroup>
    <!-- Reference to Core module -->
    <ProjectReference Include="..\Core\ArtDesignFramework.Core.csproj" />
    <ProjectReference Include="..\EffectsEngine\ArtDesignFramework.EffectsEngine.csproj" />
    <ProjectReference Include="..\UserInterface\ArtDesignFramework.UserInterface.csproj" />
  </ItemGroup>

  <ItemGroup>
    <!-- AI and Computer Vision packages -->
    <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.16.3" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.9" />
  </ItemGroup>

</Project>
