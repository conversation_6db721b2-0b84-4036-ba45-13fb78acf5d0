using System.Collections.Concurrent;
using System.Diagnostics;
using System.Reactive.Subjects;
using ArtDesignFramework.DataAccess.Entities;
using ArtDesignFramework.DataAccess.Repositories;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ArtDesignFramework.Performance.Monitoring;

/// <summary>
/// Comprehensive performance monitoring implementation
/// </summary>
public class PerformanceMonitor : IPerformanceMonitor, IDisposable
{
    private readonly ILogger<PerformanceMonitor> _logger;
    private readonly Subject<PerformanceMetrics> _metricsSubject = new();
    private readonly ConcurrentDictionary<string, OperationStatistics> _operationStats = new();
    private readonly Timer? _monitoringTimer;
    private readonly PerformanceCounter? _cpuCounter;
    private readonly PerformanceCounter? _memoryCounter;
    private readonly object _lockObject = new();
    private readonly IPerformanceRepository? _performanceRepository;
    private readonly PerformanceOptions _options;
    private readonly Timer? _databaseStorageTimer;

    private bool _isMonitoring;
    private bool _disposed;
    private PerformanceMetrics _currentMetrics = new();

    /// <summary>
    /// Initializes a new instance of the PerformanceMonitor class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="options">Performance options</param>
    /// <param name="performanceRepository">Optional performance repository for database storage</param>
    public PerformanceMonitor(
        ILogger<PerformanceMonitor> logger,
        IOptions<PerformanceOptions>? options = null,
        IPerformanceRepository? performanceRepository = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? new PerformanceOptions();
        _performanceRepository = performanceRepository;

        try
        {
            // Initialize performance counters (Windows-specific)
            if (OperatingSystem.IsWindows())
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to initialize performance counters. Some metrics may not be available.");
        }

        // Initialize monitoring timer
        _monitoringTimer = new Timer(CollectMetrics, null, Timeout.Infinite, Timeout.Infinite);

        // Initialize database storage timer if enabled
        if (_options.EnableDatabaseStorage && _performanceRepository != null)
        {
            _databaseStorageTimer = new Timer(StoreDatabaseMetrics, null, Timeout.Infinite, Timeout.Infinite);
        }
    }

    /// <inheritdoc />
    public PerformanceMetrics CurrentMetrics
    {
        get
        {
            lock (_lockObject)
            {
                return _currentMetrics;
            }
        }
    }

    /// <inheritdoc />
    public IObservable<PerformanceMetrics> MetricsStream => _metricsSubject.AsObservable();

    /// <inheritdoc />
    public async Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_isMonitoring)
            return;

        _logger.LogInformation("Starting performance monitoring");

        _isMonitoring = true;

        // Start the monitoring timer (collect metrics every second)
        _monitoringTimer?.Change(TimeSpan.Zero, TimeSpan.FromSeconds(1));

        // Start database storage timer if enabled (store every 30 seconds to avoid overwhelming database)
        if (_options.EnableDatabaseStorage && _databaseStorageTimer != null)
        {
            _databaseStorageTimer.Change(TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        await Task.CompletedTask;
    }

    /// <inheritdoc />
    public async Task StopMonitoringAsync()
    {
        if (!_isMonitoring)
            return;

        _logger.LogInformation("Stopping performance monitoring");

        _isMonitoring = false;
        _monitoringTimer?.Change(Timeout.Infinite, Timeout.Infinite);
        _databaseStorageTimer?.Change(Timeout.Infinite, Timeout.Infinite);

        await Task.CompletedTask;
    }

    /// <inheritdoc />
    public void RecordEvent(string eventName, TimeSpan duration, IDictionary<string, object>? metadata = null)
    {
        if (string.IsNullOrEmpty(eventName))
            throw new ArgumentException("Event name cannot be null or empty", nameof(eventName));

        var stats = _operationStats.GetOrAdd(eventName, _ => new OperationStatistics { OperationName = eventName });

        lock (stats)
        {
            stats.TotalExecutions++;
            stats.TotalTime = stats.TotalTime.Add(duration);
            stats.LastExecution = DateTime.UtcNow;

            if (duration < stats.MinTime)
                stats.MinTime = duration;

            if (duration > stats.MaxTime)
                stats.MaxTime = duration;

            // Update percentiles
            stats.RecentExecutions.Enqueue(duration);
            if (stats.RecentExecutions.Count > 1000) // Keep last 1000 executions
                stats.RecentExecutions.Dequeue();

            UpdatePercentiles(stats);
        }

        _logger.LogDebug("Recorded performance event: {EventName} took {Duration}ms",
            eventName, duration.TotalMilliseconds);
    }

    /// <inheritdoc />
    public IPerformanceMeasurement BeginMeasurement(string operationName)
    {
        return new PerformanceMeasurement(operationName, this);
    }

    /// <inheritdoc />
    public OperationStatistics? GetOperationStatistics(string operationName)
    {
        return _operationStats.TryGetValue(operationName, out var stats) ? stats : null;
    }

    /// <inheritdoc />
    public IEnumerable<OperationStatistics> GetAllStatistics()
    {
        return _operationStats.Values.ToList();
    }

    /// <inheritdoc />
    public void ResetStatistics()
    {
        _operationStats.Clear();
        _logger.LogInformation("Performance statistics reset");
    }

    private async void StoreDatabaseMetrics(object? state)
    {
        if (!_isMonitoring || _disposed || _performanceRepository == null)
            return;

        try
        {
            _logger.LogDebug("💾 Storing performance metrics in database");

            var sessionId = Guid.NewGuid();
            var currentTime = DateTime.UtcNow;

            // Store current performance metrics
            var currentMetrics = CurrentMetrics;
            if (currentMetrics != null)
            {
                var performanceBenchmark = new PerformanceBenchmark
                {
                    OperationName = "System Performance Monitoring",
                    Category = "SystemMetrics",
                    Subcategory = "RealTime",
                    ExecutionTimeMs = 0, // Not applicable for system metrics
                    MemoryUsageBytes = currentMetrics.MemoryUsageBytes,
                    CpuUsagePercent = currentMetrics.CpuUsagePercent,
                    ThroughputOpsPerSecond = 0, // Not applicable for system metrics
                    Value = currentMetrics.CpuUsagePercent,
                    Unit = "Percent",
                    Description = "Real-time system performance metrics",
                    SessionId = sessionId,
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                    MachineName = Environment.MachineName,
                    FrameworkVersion = "2.0.0",
                    BuildVersion = "2.0.0",
                    Tags = "system,realtime,monitoring",
                    Notes = $"CPU: {currentMetrics.CpuUsagePercent:F1}%, Memory: {currentMetrics.MemoryUsageBytes / (1024.0 * 1024):F1}MB",
                    CustomMetricsJson = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        CpuUsagePercent = currentMetrics.CpuUsagePercent,
                        MemoryUsageBytes = currentMetrics.MemoryUsageBytes,
                        AvailableMemoryBytes = currentMetrics.AvailableMemoryBytes,
                        GarbageCollection = currentMetrics.GarbageCollection,
                        ThreadPool = currentMetrics.ThreadPool
                    })
                };

                await _performanceRepository.AddAsync(performanceBenchmark);
            }

            // Store operation statistics
            var allStats = GetAllStatistics().ToList();
            foreach (var stats in allStats.Take(10)) // Limit to top 10 operations to avoid overwhelming database
            {
                if (stats.TotalExecutions > 0)
                {
                    var operationBenchmark = new PerformanceBenchmark
                    {
                        OperationName = stats.OperationName,
                        Category = "Operations",
                        Subcategory = "Statistics",
                        ExecutionTimeMs = stats.AverageTime.TotalMilliseconds,
                        MemoryUsageBytes = 0, // Not tracked per operation
                        CpuUsagePercent = 0, // Not tracked per operation
                        ThroughputOpsPerSecond = stats.TotalExecutions / Math.Max(1, (currentTime - stats.LastExecution).TotalSeconds),
                        Value = stats.AverageTime.TotalMilliseconds,
                        Unit = "Milliseconds",
                        Description = $"Performance statistics for operation: {stats.OperationName}",
                        SessionId = sessionId,
                        Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                        MachineName = Environment.MachineName,
                        FrameworkVersion = "2.0.0",
                        BuildVersion = "2.0.0",
                        Tags = $"operation,statistics,{stats.OperationName.ToLowerInvariant()}",
                        Notes = $"Executions: {stats.TotalExecutions}, Avg: {stats.AverageTime.TotalMilliseconds:F1}ms",
                        CustomMetricsJson = System.Text.Json.JsonSerializer.Serialize(new
                        {
                            TotalExecutions = stats.TotalExecutions,
                            AverageTime = stats.AverageTime,
                            MinTime = stats.MinTime,
                            MaxTime = stats.MaxTime,
                            P95Time = stats.P95Time,
                            P99Time = stats.P99Time,
                            LastExecution = stats.LastExecution
                        })
                    };

                    await _performanceRepository.AddAsync(operationBenchmark);
                }
            }

            _logger.LogDebug("✅ Performance metrics stored in database successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to store performance metrics in database");
            // Don't throw - database storage failure shouldn't break performance monitoring
        }
    }

    private void CollectMetrics(object? state)
    {
        if (!_isMonitoring || _disposed)
            return;

        try
        {
            var metrics = new PerformanceMetrics
            {
                Timestamp = DateTime.UtcNow
            };

            // Collect CPU metrics
            CollectCpuMetrics(metrics);

            // Collect memory metrics
            CollectMemoryMetrics(metrics);

            // Collect GC metrics
            CollectGarbageCollectionMetrics(metrics);

            // Collect thread pool metrics
            CollectThreadPoolMetrics(metrics);

            // Update current metrics
            lock (_lockObject)
            {
                _currentMetrics = metrics;
            }

            // Publish metrics
            _metricsSubject.OnNext(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting performance metrics");
        }
    }

    private void CollectCpuMetrics(PerformanceMetrics metrics)
    {
        try
        {
            if (_cpuCounter != null)
            {
                metrics.CpuUsagePercent = _cpuCounter.NextValue();
            }
            else
            {
                // Fallback method for non-Windows platforms
                var process = Process.GetCurrentProcess();
                metrics.CpuUsagePercent = process.TotalProcessorTime.TotalMilliseconds / Environment.TickCount * 100;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect CPU metrics");
        }
    }

    private void CollectMemoryMetrics(PerformanceMetrics metrics)
    {
        try
        {
            var process = Process.GetCurrentProcess();
            metrics.MemoryUsageBytes = process.WorkingSet64;

            if (_memoryCounter != null)
            {
                metrics.AvailableMemoryBytes = (long)_memoryCounter.NextValue() * 1024 * 1024; // Convert MB to bytes
            }
            else
            {
                // Fallback for non-Windows platforms
                var gcInfo = GC.GetGCMemoryInfo();
                metrics.AvailableMemoryBytes = gcInfo.TotalAvailableMemoryBytes;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect memory metrics");
        }
    }

    private void CollectGarbageCollectionMetrics(PerformanceMetrics metrics)
    {
        try
        {
            metrics.GarbageCollection.Gen0Collections = GC.CollectionCount(0);
            metrics.GarbageCollection.Gen1Collections = GC.CollectionCount(1);
            metrics.GarbageCollection.Gen2Collections = GC.CollectionCount(2);
            metrics.GarbageCollection.TotalAllocatedBytes = GC.GetTotalAllocatedBytes();

            var gcInfo = GC.GetGCMemoryInfo();
            metrics.GarbageCollection.MemoryBeforeLastCollection = gcInfo.HeapSizeBytes;
            metrics.GarbageCollection.MemoryAfterLastCollection = gcInfo.FragmentedBytes;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect GC metrics");
        }
    }

    private void CollectThreadPoolMetrics(PerformanceMetrics metrics)
    {
        try
        {
            ThreadPool.GetAvailableThreads(out var workerThreads, out var completionPortThreads);
            ThreadPool.GetMaxThreads(out var maxWorkerThreads, out var maxCompletionPortThreads);

            metrics.ThreadPool.WorkerThreads = maxWorkerThreads - workerThreads;
            metrics.ThreadPool.CompletionPortThreads = maxCompletionPortThreads - completionPortThreads;
            metrics.ThreadPool.MaxWorkerThreads = maxWorkerThreads;
            metrics.ThreadPool.MaxCompletionPortThreads = maxCompletionPortThreads;

            // Get pending work items (if available)
            ThreadPool.GetPendingWorkItemCount(out var pendingItems);
            metrics.ThreadPool.QueuedWorkItems = pendingItems;

            ThreadPool.GetCompletedWorkItemCount(out var completedItems);
            metrics.ThreadPool.CompletedWorkItems = completedItems;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect thread pool metrics");
        }
    }

    private static void UpdatePercentiles(OperationStatistics stats)
    {
        if (stats.RecentExecutions.Count < 10)
            return;

        var sortedTimes = stats.RecentExecutions.OrderBy(t => t.Ticks).ToArray();
        var count = sortedTimes.Length;

        stats.P95Time = sortedTimes[(int)(count * 0.95)];
        stats.P99Time = sortedTimes[(int)(count * 0.99)];
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        _monitoringTimer?.Dispose();
        _databaseStorageTimer?.Dispose();
        _cpuCounter?.Dispose();
        _memoryCounter?.Dispose();
        _metricsSubject.Dispose();

        _logger.LogInformation("Performance monitor disposed");
    }
}

/// <summary>
/// Performance measurement implementation
/// </summary>
internal class PerformanceMeasurement : IPerformanceMeasurement
{
    private readonly PerformanceMonitor _monitor;
    private readonly Stopwatch _stopwatch;
    private readonly Dictionary<string, object> _metadata = new();
    private bool _disposed;
    private bool _failed;
    private Exception? _exception;

    public PerformanceMeasurement(string operationName, PerformanceMonitor monitor)
    {
        OperationName = operationName ?? throw new ArgumentNullException(nameof(operationName));
        _monitor = monitor ?? throw new ArgumentNullException(nameof(monitor));
        StartTime = DateTime.UtcNow;
        _stopwatch = Stopwatch.StartNew();
    }

    /// <inheritdoc />
    public string OperationName { get; }

    /// <inheritdoc />
    public DateTime StartTime { get; }

    /// <inheritdoc />
    public TimeSpan Elapsed => _stopwatch.Elapsed;

    /// <inheritdoc />
    public void AddMetadata(string key, object value)
    {
        if (string.IsNullOrEmpty(key))
            throw new ArgumentException("Key cannot be null or empty", nameof(key));

        _metadata[key] = value;
    }

    /// <inheritdoc />
    public void MarkAsFailed(Exception? exception = null)
    {
        _failed = true;
        _exception = exception;
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _stopwatch.Stop();

        // Record the measurement
        _monitor.RecordEvent(OperationName, _stopwatch.Elapsed, _metadata);

        // Log if failed
        if (_failed && _exception != null)
        {
            // Log the failure (would need access to logger)
        }
    }
}
