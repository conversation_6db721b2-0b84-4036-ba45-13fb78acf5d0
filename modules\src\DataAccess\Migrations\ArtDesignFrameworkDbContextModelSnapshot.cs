// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System;
using ArtDesignFramework.DataAccess.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;

#nullable disable

namespace ArtDesignFramework.DataAccess.Migrations
{
    /// <summary>
    /// Entity Framework model snapshot for ArtDesignFrameworkDbContext
    /// </summary>
    [DbContext(typeof(ArtDesignFrameworkDbContext))]
    partial class ArtDesignFrameworkDbContextModelSnapshot : ModelSnapshot
    {
        /// <summary>
        /// Builds the model snapshot
        /// </summary>
        /// <param name="modelBuilder">Model builder</param>
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.0");

            modelBuilder.Entity("ArtDesignFramework.DataAccess.Entities.FrameworkConfiguration", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("AllowedValuesJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ChangeHistoryJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("ConfigurationKey")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("ConfigurationValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Environment")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FrameworkVersion")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsEncrypted")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsFeatureFlag")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("IsReadOnly")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MachineName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("MaxValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("MetadataJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("MinValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ModuleName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<bool>("RequiresRestart")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Scope")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Source")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Subcategory")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Tags")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("UpdatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ValidationErrorMessage")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("ValidationPattern")
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("ConfigurationKey")
                        .IsUnique();

                    b.HasIndex("Environment");

                    b.HasIndex("IsActive");

                    b.HasIndex("Scope");

                    b.HasIndex("Category", "ConfigurationKey");

                    b.HasIndex("Scope", "Environment", "ConfigurationKey");

                    b.ToTable("FrameworkConfigurations");
                });

            modelBuilder.Entity("ArtDesignFramework.DataAccess.Entities.ModuleHealthStatus", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("BuildVersion")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimedStatus")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CurrentStatus")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("DependenciesJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("Environment")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FrameworkVersion")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MachineName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ModuleName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("PerformanceMetricsJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("PreviousStatus")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("RecommendationsJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("SecurityScanResultsJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("StatusChangedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Tags")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("UpdatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("ValidationIssuesJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("Version")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("CurrentStatus");

                    b.HasIndex("ModuleName");

                    b.HasIndex("SessionId");

                    b.HasIndex("ModuleName", "CreatedAt");

                    b.ToTable("ModuleHealthStatuses");
                });

            modelBuilder.Entity("ArtDesignFramework.DataAccess.Entities.PerformanceBenchmark", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("BuildVersion")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<double>("CpuUsagePercent")
                        .HasColumnType("REAL");

                    b.Property<string>("CreatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomMetricsJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("Environment")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<double>("ExecutionTimeMs")
                        .HasColumnType("REAL");

                    b.Property<string>("FrameworkVersion")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MachineName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<long>("MemoryUsageBytes")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("OperationName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("RenderingQuality")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Subcategory")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Tags")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("TestDataSize")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<double>("ThroughputOpsPerSecond")
                        .HasColumnType("REAL");

                    b.Property<string>("Unit")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("UpdatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<double>("Value")
                        .HasColumnType("REAL");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("OperationName");

                    b.HasIndex("SessionId");

                    b.HasIndex("Category", "OperationName", "CreatedAt");

                    b.ToTable("PerformanceBenchmarks");
                });

            modelBuilder.Entity("ArtDesignFramework.DataAccess.Entities.TestExecutionResult", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("BuildVersion")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ContextJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("CustomMetricsJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("Environment")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ErrorDetails")
                        .HasColumnType("TEXT");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<long>("ExecutionTimeMs")
                        .HasColumnType("INTEGER");

                    b.Property<string>("FrameworkVersion")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MachineName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<bool>("Passed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Priority")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("Tags")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("TestCategory")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("TestCode")
                        .HasColumnType("TEXT");

                    b.Property<string>("TestName")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("TEXT");

                    b.Property<string>("TestParametersJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("TestRunnerVersion")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("TestSuite")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("UpdatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Passed");

                    b.HasIndex("SessionId");

                    b.HasIndex("TestName");

                    b.HasIndex("TestSuite");

                    b.HasIndex("TestSuite", "TestName", "CreatedAt");

                    b.ToTable("TestExecutionResults");
                });

            modelBuilder.Entity("ArtDesignFramework.DataAccess.Entities.UserPreference", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("Application")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("ApplicationVersion")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("BrowserInfo")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("ChangeHistoryJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("ComponentName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("CreatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("DeviceId")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("DeviceType")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Environment")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("FrameworkVersion")
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<bool>("IsActive")
                        .HasColumnType("INTEGER");

                    b.Property<string>("MachineName")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("MetadataJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("ModuleName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("TEXT");

                    b.Property<string>("OperatingSystem")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("PreferenceKey")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("PreferenceValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("Scope")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT");

                    b.Property<string>("Subcategory")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("Tags")
                        .HasMaxLength(1000)
                        .HasColumnType("TEXT");

                    b.Property<string>("UpdatedAt")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<string>("UsageStatisticsJson")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserDisplayName")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserEmail")
                        .HasMaxLength(200)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.Property<string>("UserRole")
                        .HasMaxLength(100)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("Application");

                    b.HasIndex("Category");

                    b.HasIndex("IsActive");

                    b.HasIndex("PreferenceKey");

                    b.HasIndex("UserId");

                    b.HasIndex("UserId", "PreferenceKey")
                        .IsUnique();

                    b.HasIndex("Application", "UserId", "Category");

                    b.ToTable("UserPreferences");
                });
#pragma warning restore 612, 618
        }
    }
}
