import { execSync } from 'child_process'
import fs from 'fs'
import path from 'path'

interface TestResult {
  testSuite: string
  testName: string
  status: 'PASS' | 'FAIL' | 'SKIP'
  duration: number
  error?: string
  details?: string
}

interface TestReport {
  timestamp: string
  totalTests: number
  passed: number
  failed: number
  skipped: number
  duration: number
  results: TestResult[]
  coverage?: {
    lines: number
    functions: number
    branches: number
    statements: number
  }
}

class AutomatedTestRunner {
  private results: TestResult[] = []
  private startTime: number = 0

  async runComprehensiveTests(): Promise<TestReport> {
    console.log('🚀 Starting Comprehensive Dynamic Canvas Functional Tests...\n')
    this.startTime = Date.now()

    const report: TestReport = {
      timestamp: new Date().toISOString(),
      totalTests: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      results: []
    }

    try {
      // Run the comprehensive functional tests
      console.log('📋 Running functional tests...')
      await this.runTestSuite('comprehensive-functional.test.ts')

      // Run existing tool tests
      console.log('🔧 Running tool-specific tests...')
      await this.runTestSuite('tools/BrushTools.test.ts')

      // Run infrastructure tests
      console.log('🏗️ Running infrastructure tests...')
      await this.runTestSuite('infrastructure/TestInfrastructure.test.ts')

      // Run integration tests
      console.log('🔗 Running integration tests...')
      await this.runTestSuite('integration.test.ts')

      // Generate coverage report
      console.log('📊 Generating coverage report...')
      const coverage = await this.generateCoverageReport()

      // Compile final report
      report.results = this.results
      report.totalTests = this.results.length
      report.passed = this.results.filter(r => r.status === 'PASS').length
      report.failed = this.results.filter(r => r.status === 'FAIL').length
      report.skipped = this.results.filter(r => r.status === 'SKIP').length
      report.duration = Date.now() - this.startTime
      report.coverage = coverage

      // Save detailed report
      await this.saveReport(report)

      // Display summary
      this.displaySummary(report)

      return report

    } catch (error) {
      console.error('❌ Test execution failed:', error)
      throw error
    }
  }

  private async runTestSuite(testFile: string): Promise<void> {
    try {
      const testPath = path.join(__dirname, testFile)
      
      if (!fs.existsSync(testPath)) {
        console.log(`⚠️  Test file not found: ${testFile}`)
        return
      }

      console.log(`   Running: ${testFile}`)
      
      // Run vitest for specific file
      const command = `npx vitest run ${testFile} --reporter=json`
      const output = execSync(command, { 
        cwd: path.join(__dirname, '../..'),
        encoding: 'utf8',
        timeout: 300000 // 5 minutes timeout
      })

      // Parse vitest JSON output
      const testResults = this.parseVitestOutput(output, testFile)
      this.results.push(...testResults)

    } catch (error) {
      console.error(`❌ Failed to run ${testFile}:`, error)
      this.results.push({
        testSuite: testFile,
        testName: 'Test Suite Execution',
        status: 'FAIL',
        duration: 0,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  private parseVitestOutput(output: string, testFile: string): TestResult[] {
    const results: TestResult[] = []
    
    try {
      const jsonOutput = JSON.parse(output)
      
      if (jsonOutput.testResults) {
        for (const testResult of jsonOutput.testResults) {
          for (const assertionResult of testResult.assertionResults || []) {
            results.push({
              testSuite: testFile,
              testName: assertionResult.title || assertionResult.fullName,
              status: assertionResult.status === 'passed' ? 'PASS' : 
                      assertionResult.status === 'skipped' ? 'SKIP' : 'FAIL',
              duration: assertionResult.duration || 0,
              error: assertionResult.failureMessages?.join('\n'),
              details: assertionResult.ancestorTitles?.join(' > ')
            })
          }
        }
      }
    } catch (parseError) {
      // Fallback parsing for non-JSON output
      console.log(`⚠️  Could not parse JSON output for ${testFile}, using fallback`)
      
      // Simple pattern matching for test results
      const lines = output.split('\n')
      let currentSuite = ''
      
      for (const line of lines) {
        if (line.includes('PASS') || line.includes('FAIL') || line.includes('SKIP')) {
          const status = line.includes('PASS') ? 'PASS' : 
                       line.includes('SKIP') ? 'SKIP' : 'FAIL'
          
          results.push({
            testSuite: testFile,
            testName: line.trim(),
            status,
            duration: 0
          })
        }
      }
    }

    return results
  }

  private async generateCoverageReport(): Promise<any> {
    try {
      const command = 'npx vitest run --coverage --reporter=json'
      const output = execSync(command, { 
        cwd: path.join(__dirname, '../..'),
        encoding: 'utf8',
        timeout: 120000 // 2 minutes timeout
      })

      const coverageData = JSON.parse(output)
      return coverageData.coverage || {
        lines: 0,
        functions: 0,
        branches: 0,
        statements: 0
      }
    } catch (error) {
      console.log('⚠️  Could not generate coverage report')
      return {
        lines: 0,
        functions: 0,
        branches: 0,
        statements: 0
      }
    }
  }

  private async saveReport(report: TestReport): Promise<void> {
    const reportDir = path.join(__dirname, '../../../test-reports')
    
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true })
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const reportFile = path.join(reportDir, `comprehensive-test-report-${timestamp}.json`)
    
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2))
    
    // Also create an HTML report
    const htmlReport = this.generateHtmlReport(report)
    const htmlFile = path.join(reportDir, `comprehensive-test-report-${timestamp}.html`)
    fs.writeFileSync(htmlFile, htmlReport)

    console.log(`📄 Reports saved:`)
    console.log(`   JSON: ${reportFile}`)
    console.log(`   HTML: ${htmlFile}`)
  }

  private generateHtmlReport(report: TestReport): string {
    const passRate = ((report.passed / report.totalTests) * 100).toFixed(1)
    
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Dynamic Canvas Comprehensive Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e8f4fd; padding: 15px; border-radius: 5px; text-align: center; }
        .pass { background: #d4edda; }
        .fail { background: #f8d7da; }
        .skip { background: #fff3cd; }
        .test-results { margin-top: 20px; }
        .test-suite { margin-bottom: 20px; }
        .test-case { padding: 10px; margin: 5px 0; border-left: 4px solid #ccc; }
        .test-case.pass { border-color: #28a745; background: #f8fff9; }
        .test-case.fail { border-color: #dc3545; background: #fff8f8; }
        .test-case.skip { border-color: #ffc107; background: #fffef8; }
        .error { color: #dc3545; font-family: monospace; font-size: 12px; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Dynamic Canvas Comprehensive Test Report</h1>
        <p><strong>Generated:</strong> ${report.timestamp}</p>
        <p><strong>Duration:</strong> ${(report.duration / 1000).toFixed(2)}s</p>
    </div>

    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <div style="font-size: 24px; font-weight: bold;">${report.totalTests}</div>
        </div>
        <div class="metric pass">
            <h3>Passed</h3>
            <div style="font-size: 24px; font-weight: bold;">${report.passed}</div>
        </div>
        <div class="metric fail">
            <h3>Failed</h3>
            <div style="font-size: 24px; font-weight: bold;">${report.failed}</div>
        </div>
        <div class="metric skip">
            <h3>Skipped</h3>
            <div style="font-size: 24px; font-weight: bold;">${report.skipped}</div>
        </div>
        <div class="metric">
            <h3>Pass Rate</h3>
            <div style="font-size: 24px; font-weight: bold;">${passRate}%</div>
        </div>
    </div>

    ${report.coverage ? `
    <div class="summary">
        <div class="metric">
            <h3>Line Coverage</h3>
            <div style="font-size: 18px; font-weight: bold;">${report.coverage.lines}%</div>
        </div>
        <div class="metric">
            <h3>Function Coverage</h3>
            <div style="font-size: 18px; font-weight: bold;">${report.coverage.functions}%</div>
        </div>
        <div class="metric">
            <h3>Branch Coverage</h3>
            <div style="font-size: 18px; font-weight: bold;">${report.coverage.branches}%</div>
        </div>
    </div>
    ` : ''}

    <div class="test-results">
        <h2>Test Results</h2>
        ${this.groupTestsBySuite(report.results).map(suite => `
            <div class="test-suite">
                <h3>${suite.name}</h3>
                ${suite.tests.map(test => `
                    <div class="test-case ${test.status.toLowerCase()}">
                        <strong>${test.testName}</strong>
                        <span style="float: right;">${test.status} (${test.duration}ms)</span>
                        ${test.error ? `<div class="error">${test.error}</div>` : ''}
                    </div>
                `).join('')}
            </div>
        `).join('')}
    </div>
</body>
</html>
    `
  }

  private groupTestsByuite(results: TestResult[]): Array<{name: string, tests: TestResult[]}> {
    const suites = new Map<string, TestResult[]>()
    
    for (const result of results) {
      if (!suites.has(result.testSuite)) {
        suites.set(result.testSuite, [])
      }
      suites.get(result.testSuite)!.push(result)
    }

    return Array.from(suites.entries()).map(([name, tests]) => ({ name, tests }))
  }

  private displaySummary(report: TestReport): void {
    console.log('\n' + '='.repeat(60))
    console.log('🎯 COMPREHENSIVE TEST SUMMARY')
    console.log('='.repeat(60))
    console.log(`📊 Total Tests: ${report.totalTests}`)
    console.log(`✅ Passed: ${report.passed}`)
    console.log(`❌ Failed: ${report.failed}`)
    console.log(`⏭️  Skipped: ${report.skipped}`)
    console.log(`⏱️  Duration: ${(report.duration / 1000).toFixed(2)}s`)
    console.log(`📈 Pass Rate: ${((report.passed / report.totalTests) * 100).toFixed(1)}%`)
    
    if (report.coverage) {
      console.log('\n📋 COVERAGE SUMMARY')
      console.log(`Lines: ${report.coverage.lines}%`)
      console.log(`Functions: ${report.coverage.functions}%`)
      console.log(`Branches: ${report.coverage.branches}%`)
    }

    if (report.failed > 0) {
      console.log('\n❌ FAILED TESTS:')
      report.results
        .filter(r => r.status === 'FAIL')
        .forEach(test => {
          console.log(`   ${test.testSuite} > ${test.testName}`)
          if (test.error) {
            console.log(`      Error: ${test.error.split('\n')[0]}`)
          }
        })
    }

    console.log('\n' + '='.repeat(60))
    
    if (report.failed === 0) {
      console.log('🎉 ALL TESTS PASSED! The Dynamic Canvas app is functioning correctly.')
    } else {
      console.log(`⚠️  ${report.failed} test(s) failed. Please review the detailed report.`)
    }
  }
}

// Export for use in other scripts
export { AutomatedTestRunner, TestResult, TestReport }

// Run tests if this file is executed directly
if (require.main === module) {
  const runner = new AutomatedTestRunner()
  runner.runComprehensiveTests()
    .then(report => {
      process.exit(report.failed > 0 ? 1 : 0)
    })
    .catch(error => {
      console.error('Test execution failed:', error)
      process.exit(1)
    })
}
