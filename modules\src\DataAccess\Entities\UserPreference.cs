// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArtDesignFramework.DataAccess.Entities;

/// <summary>
/// Entity representing user preferences and application state
/// </summary>
[Table("UserPreferences")]
public class UserPreference
{
    /// <summary>
    /// Gets or sets the unique identifier for the user preference
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Gets or sets the user identifier
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the preference key (unique identifier for the setting)
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string PreferenceKey { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the preference value
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? PreferenceValue { get; set; }

    /// <summary>
    /// Gets or sets the data type of the preference value
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string DataType { get; set; } = "String";

    /// <summary>
    /// Gets or sets the default value for this preference
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? DefaultValue { get; set; }

    /// <summary>
    /// Gets or sets the preference category for organization
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Category { get; set; } = "General";

    /// <summary>
    /// Gets or sets the subcategory for more granular organization
    /// </summary>
    [MaxLength(100)]
    public string? Subcategory { get; set; }

    /// <summary>
    /// Gets or sets the preference description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the application or module this preference belongs to
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Application { get; set; } = "ArtDesignFramework";

    /// <summary>
    /// Gets or sets the module name if this is module-specific preference
    /// </summary>
    [MaxLength(200)]
    public string? ModuleName { get; set; }

    /// <summary>
    /// Gets or sets the component name if this is component-specific preference
    /// </summary>
    [MaxLength(200)]
    public string? ComponentName { get; set; }

    /// <summary>
    /// Gets or sets whether this preference is synchronized across devices
    /// </summary>
    public bool IsSynchronized { get; set; } = true;

    /// <summary>
    /// Gets or sets whether this preference is encrypted
    /// </summary>
    public bool IsEncrypted { get; set; }

    /// <summary>
    /// Gets or sets whether this preference is sensitive (should not be logged)
    /// </summary>
    public bool IsSensitive { get; set; }

    /// <summary>
    /// Gets or sets whether this preference is temporary (session-only)
    /// </summary>
    public bool IsTemporary { get; set; }

    /// <summary>
    /// Gets or sets the preference scope (User, Machine, Global)
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Scope { get; set; } = "User";

    /// <summary>
    /// Gets or sets the machine name if this is machine-specific preference
    /// </summary>
    [MaxLength(100)]
    public string? MachineName { get; set; }

    /// <summary>
    /// Gets or sets the environment where this preference applies
    /// </summary>
    [MaxLength(100)]
    public string Environment { get; set; } = "All";

    /// <summary>
    /// Gets or sets the framework version this preference applies to
    /// </summary>
    [MaxLength(50)]
    public string? FrameworkVersion { get; set; }

    /// <summary>
    /// Gets or sets the application version this preference applies to
    /// </summary>
    [MaxLength(50)]
    public string? ApplicationVersion { get; set; }

    /// <summary>
    /// Gets or sets the user's display name for reference
    /// </summary>
    [MaxLength(200)]
    public string? UserDisplayName { get; set; }

    /// <summary>
    /// Gets or sets the user's email for reference
    /// </summary>
    [MaxLength(200)]
    public string? UserEmail { get; set; }

    /// <summary>
    /// Gets or sets the user's role or permission level
    /// </summary>
    [MaxLength(100)]
    public string? UserRole { get; set; }

    /// <summary>
    /// Gets or sets the session identifier when this preference was set
    /// </summary>
    public Guid? SessionId { get; set; }

    /// <summary>
    /// Gets or sets the device identifier where this preference was set
    /// </summary>
    [MaxLength(200)]
    public string? DeviceId { get; set; }

    /// <summary>
    /// Gets or sets the device type (Desktop, Mobile, Web, etc.)
    /// </summary>
    [MaxLength(50)]
    public string? DeviceType { get; set; }

    /// <summary>
    /// Gets or sets the operating system where this preference was set
    /// </summary>
    [MaxLength(100)]
    public string? OperatingSystem { get; set; }

    /// <summary>
    /// Gets or sets the browser information if applicable
    /// </summary>
    [MaxLength(200)]
    public string? BrowserInfo { get; set; }

    /// <summary>
    /// Gets or sets the preference metadata as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? MetadataJson { get; set; }

    /// <summary>
    /// Gets or sets the preference change history as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? ChangeHistoryJson { get; set; }

    /// <summary>
    /// Gets or sets usage statistics as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? UsageStatisticsJson { get; set; }

    /// <summary>
    /// Gets or sets when the preference was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the preference was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the preference was last accessed
    /// </summary>
    public DateTime? LastAccessedAt { get; set; }

    /// <summary>
    /// Gets or sets how many times this preference has been accessed
    /// </summary>
    public long AccessCount { get; set; } = 0;

    /// <summary>
    /// Gets or sets when this preference expires (for temporary preferences)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Gets or sets whether this preference is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Gets or sets tags associated with this preference
    /// </summary>
    [MaxLength(1000)]
    public string? Tags { get; set; }

    /// <summary>
    /// Gets or sets additional notes or comments
    /// </summary>
    [MaxLength(2000)]
    public string? Notes { get; set; }
}
