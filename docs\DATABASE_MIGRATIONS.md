# ArtDesignFramework Database Migrations Guide

**Last Updated:** 2025-06-08 21:30:00 UTC

## Overview

This guide covers Entity Framework Core migration procedures and best practices for the ArtDesignFramework database system. Migrations provide version control for database schema changes and enable safe deployment of database updates across environments.

## Migration Basics

### What are Migrations?
Migrations are code-first database schema changes that:
- Track database schema evolution over time
- Enable safe deployment of schema changes
- Provide rollback capabilities
- Maintain data integrity during schema updates

### Migration Files Location
```
modules/src/DataAccess/Migrations/
├── 20250608000000_InitialCreate.cs
├── 20250608000000_InitialCreate.Designer.cs
└── ArtDesignFrameworkDbContextModelSnapshot.cs
```

## Current Migration Status

### InitialCreate Migration (Version 1.0)
**Created:** 2025-06-08 21:00:00 UTC  
**Status:** Applied  
**Description:** Initial database schema creation

#### Tables Created:
- TestExecutionResults
- PerformanceBenchmarks  
- ModuleHealthStatuses
- FrameworkConfigurations
- UserPreferences

#### Indexes Created:
- Performance optimization indexes on frequently queried columns
- Composite indexes for common query patterns
- Unique constraints where appropriate

## Migration Commands

### Creating New Migrations

#### Basic Migration Creation
```bash
# Navigate to DataAccess project
cd modules/src/DataAccess

# Create new migration
dotnet ef migrations add [MigrationName]
```

#### Migration with Custom Output Directory
```bash
dotnet ef migrations add [MigrationName] --output-dir Migrations
```

#### Migration with Specific Context
```bash
dotnet ef migrations add [MigrationName] --context ArtDesignFrameworkDbContext
```

### Applying Migrations

#### Apply All Pending Migrations
```bash
dotnet ef database update
```

#### Apply Specific Migration
```bash
dotnet ef database update [MigrationName]
```

#### Apply Migration to Specific Environment
```bash
dotnet ef database update --environment Production
```

### Migration Information

#### List All Migrations
```bash
dotnet ef migrations list
```

#### Check Migration Status
```bash
dotnet ef migrations has-pending-model-changes
```

#### Generate SQL Script
```bash
dotnet ef migrations script
dotnet ef migrations script [FromMigration] [ToMigration]
```

## Migration Best Practices

### Naming Conventions
- Use descriptive names: `AddUserPreferencesTable`
- Include date for major changes: `20250608_AddPerformanceIndexes`
- Use PascalCase: `UpdateTestResultsSchema`

### Migration Content Guidelines

#### Safe Operations
- Adding new tables
- Adding new columns (with default values)
- Adding indexes
- Adding constraints (with validation)

#### Potentially Breaking Operations
- Dropping tables or columns
- Changing column types
- Adding non-nullable columns without defaults
- Removing indexes used by application code

### Data Migration Patterns

#### Adding New Column with Default Value
```csharp
protected override void Up(MigrationBuilder migrationBuilder)
{
    migrationBuilder.AddColumn<string>(
        name: "NewColumn",
        table: "TableName",
        type: "NVARCHAR(100)",
        nullable: false,
        defaultValue: "DefaultValue");
}
```

#### Safe Column Type Change
```csharp
protected override void Up(MigrationBuilder migrationBuilder)
{
    // Step 1: Add new column
    migrationBuilder.AddColumn<int>(
        name: "NewColumnName",
        table: "TableName",
        type: "INTEGER",
        nullable: true);
    
    // Step 2: Migrate data (in separate migration)
    // Step 3: Drop old column (in separate migration)
}
```

## Environment-Specific Migrations

### Development Environment
```bash
# Development database updates
dotnet ef database update --environment Development
```

### Production Environment
```bash
# Generate production script for review
dotnet ef migrations script --environment Production --output migration.sql

# Apply after review
dotnet ef database update --environment Production
```

### Testing Environment
```bash
# Reset test database
dotnet ef database drop --environment Testing
dotnet ef database update --environment Testing
```

## Migration Rollback Procedures

### Rollback to Previous Migration
```bash
# Rollback one migration
dotnet ef database update [PreviousMigrationName]
```

### Rollback to Initial State
```bash
# Remove all migrations (development only)
dotnet ef database update 0
```

### Safe Production Rollback
```csharp
// Create rollback migration
protected override void Up(MigrationBuilder migrationBuilder)
{
    // Reverse the changes from previous migration
    migrationBuilder.DropColumn("NewColumn", "TableName");
}

protected override void Down(MigrationBuilder migrationBuilder)
{
    // Re-apply the original changes
    migrationBuilder.AddColumn<string>("NewColumn", "TableName");
}
```

## Advanced Migration Scenarios

### Data Seeding in Migrations
```csharp
protected override void Up(MigrationBuilder migrationBuilder)
{
    // Create table first
    migrationBuilder.CreateTable(/* table definition */);
    
    // Seed initial data
    migrationBuilder.InsertData(
        table: "FrameworkConfigurations",
        columns: new[] { "Id", "ConfigurationKey", "ConfigurationValue", "Category" },
        values: new object[] { Guid.NewGuid(), "DefaultTheme", "Light", "UI" });
}
```

### Custom SQL in Migrations
```csharp
protected override void Up(MigrationBuilder migrationBuilder)
{
    migrationBuilder.Sql(@"
        CREATE INDEX IX_Custom_Index 
        ON TestExecutionResults (TestName, Status, CreatedAt)
        WHERE Status = 2"); // Failed tests only
}
```

### Conditional Migrations
```csharp
protected override void Up(MigrationBuilder migrationBuilder)
{
    // Check if column exists before adding
    migrationBuilder.Sql(@"
        IF NOT EXISTS (SELECT * FROM pragma_table_info('TestExecutionResults') WHERE name='NewColumn')
        BEGIN
            ALTER TABLE TestExecutionResults ADD COLUMN NewColumn NVARCHAR(100);
        END");
}
```

## Migration Testing

### Test Migration Scripts
```bash
# Test migration on copy of production data
cp production.db test.db
dotnet ef database update --connection "Data Source=test.db"
```

### Automated Migration Testing
```csharp
[Test]
public async Task Migration_ShouldApplySuccessfully()
{
    using var context = new ArtDesignFrameworkDbContext(testOptions);
    await context.Database.MigrateAsync();
    
    // Verify schema
    var tables = await context.Database.GetAppliedMigrationsAsync();
    Assert.That(tables, Contains.Item("InitialCreate"));
}
```

## Troubleshooting Migrations

### Common Issues

#### Migration Already Applied
```bash
# Force re-apply migration (development only)
dotnet ef database update [PreviousMigration]
dotnet ef database update [TargetMigration]
```

#### Corrupted Migration State
```bash
# Reset migration history (development only)
dotnet ef migrations remove
dotnet ef migrations add InitialCreate
dotnet ef database update
```

#### Schema Drift Detection
```bash
# Check for model changes
dotnet ef migrations has-pending-model-changes
```

### Recovery Procedures

#### Backup Before Migration
```bash
# Always backup before production migration
sqlite3 ArtDesignFramework.db ".backup pre_migration_backup.db"
dotnet ef database update
```

#### Emergency Rollback
```bash
# Restore from backup if migration fails
cp pre_migration_backup.db ArtDesignFramework.db
```

## Migration Deployment

### CI/CD Integration
```yaml
# Azure DevOps pipeline example
- task: DotNetCoreCLI@2
  displayName: 'Apply Database Migrations'
  inputs:
    command: 'custom'
    custom: 'ef'
    arguments: 'database update --project modules/src/DataAccess'
```

### Docker Deployment
```dockerfile
# Apply migrations during container startup
RUN dotnet ef database update --project modules/src/DataAccess
```

### Blue-Green Deployment
```bash
# Generate migration script for review
dotnet ef migrations script --output migration.sql

# Apply to staging environment first
dotnet ef database update --environment Staging

# After validation, apply to production
dotnet ef database update --environment Production
```

## Migration Monitoring

### Performance Monitoring
```csharp
// Log migration performance
public class MigrationLogger : ILogger<DbContext>
{
    public void LogInformation(string message)
    {
        if (message.Contains("Applying migration"))
        {
            var stopwatch = Stopwatch.StartNew();
            // Log migration timing
        }
    }
}
```

### Migration Alerts
```csharp
// Monitor for failed migrations
services.AddHealthChecks()
    .AddDbContextCheck<ArtDesignFrameworkDbContext>("database")
    .AddCheck<MigrationHealthCheck>("migrations");
```

---

**Migration System Version:** 1.0  
**Entity Framework Version:** 9.0.0  
**Current Schema Version:** InitialCreate  
**Last Migration:** 2025-06-08 21:00:00 UTC
