// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace ArtDesignFramework.DataAccess.Migrations
{
    /// <summary>
    /// Initial database schema creation migration
    /// </summary>
    public partial class InitialCreate : Migration
    {
        /// <summary>
        /// Creates the initial database schema
        /// </summary>
        /// <param name="migrationBuilder">Migration builder</param>
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create TestExecutionResults table
            migrationBuilder.CreateTable(
                name: "TestExecutionResults",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    TestName = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    TestSuite = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Passed = table.Column<bool>(type: "INTEGER", nullable: false),
                    ExecutionTimeMs = table.Column<long>(type: "INTEGER", nullable: false),
                    ErrorMessage = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    ErrorDetails = table.Column<string>(type: "TEXT", nullable: true),
                    TestCode = table.Column<string>(type: "TEXT", nullable: true),
                    TestCategory = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Priority = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    SessionId = table.Column<string>(type: "TEXT", nullable: false),
                    Environment = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MachineName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    FrameworkVersion = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    TestRunnerVersion = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    BuildVersion = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Tags = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    TestParametersJson = table.Column<string>(type: "TEXT", nullable: true),
                    ContextJson = table.Column<string>(type: "TEXT", nullable: true),
                    CustomMetricsJson = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TestExecutionResults", x => x.Id);
                });

            // Create PerformanceBenchmarks table
            migrationBuilder.CreateTable(
                name: "PerformanceBenchmarks",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    OperationName = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    Category = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Subcategory = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    ExecutionTimeMs = table.Column<double>(type: "REAL", nullable: false),
                    MemoryUsageBytes = table.Column<long>(type: "INTEGER", nullable: false),
                    CpuUsagePercent = table.Column<double>(type: "REAL", nullable: false),
                    ThroughputOpsPerSecond = table.Column<double>(type: "REAL", nullable: false),
                    Value = table.Column<double>(type: "REAL", nullable: false),
                    Unit = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    TestDataSize = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    RenderingQuality = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    SessionId = table.Column<string>(type: "TEXT", nullable: false),
                    Environment = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MachineName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    FrameworkVersion = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    BuildVersion = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Tags = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    CustomMetricsJson = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PerformanceBenchmarks", x => x.Id);
                });

            // Create ModuleHealthStatuses table
            migrationBuilder.CreateTable(
                name: "ModuleHealthStatuses",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ModuleName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    Version = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    CurrentStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    PreviousStatus = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    StatusChangedAt = table.Column<string>(type: "TEXT", nullable: false),
                    ClaimedStatus = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    ValidationIssuesJson = table.Column<string>(type: "TEXT", nullable: true),
                    RecommendationsJson = table.Column<string>(type: "TEXT", nullable: true),
                    DependenciesJson = table.Column<string>(type: "TEXT", nullable: true),
                    PerformanceMetricsJson = table.Column<string>(type: "TEXT", nullable: true),
                    SecurityScanResultsJson = table.Column<string>(type: "TEXT", nullable: true),
                    SessionId = table.Column<string>(type: "TEXT", nullable: false),
                    Environment = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MachineName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    FrameworkVersion = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    BuildVersion = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Tags = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    CreatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ModuleHealthStatuses", x => x.Id);
                });

            // Create FrameworkConfigurations table
            migrationBuilder.CreateTable(
                name: "FrameworkConfigurations",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    ConfigurationKey = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    ConfigurationValue = table.Column<string>(type: "TEXT", nullable: true),
                    DataType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    DefaultValue = table.Column<string>(type: "TEXT", nullable: true),
                    MinValue = table.Column<string>(type: "TEXT", nullable: true),
                    MaxValue = table.Column<string>(type: "TEXT", nullable: true),
                    AllowedValuesJson = table.Column<string>(type: "TEXT", nullable: true),
                    Category = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Subcategory = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    IsFeatureFlag = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsEncrypted = table.Column<bool>(type: "INTEGER", nullable: false),
                    IsReadOnly = table.Column<bool>(type: "INTEGER", nullable: false),
                    RequiresRestart = table.Column<bool>(type: "INTEGER", nullable: false),
                    Scope = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    ModuleName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    UserId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    MachineName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Environment = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    FrameworkVersion = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    Source = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    CreatedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    ModifiedBy = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Tags = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    ValidationPattern = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    ValidationErrorMessage = table.Column<string>(type: "TEXT", maxLength: 500, nullable: true),
                    MetadataJson = table.Column<string>(type: "TEXT", nullable: true),
                    ChangeHistoryJson = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FrameworkConfigurations", x => x.Id);
                });

            // Create UserPreferences table
            migrationBuilder.CreateTable(
                name: "UserPreferences",
                columns: table => new
                {
                    Id = table.Column<string>(type: "TEXT", nullable: false),
                    UserId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    PreferenceKey = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    PreferenceValue = table.Column<string>(type: "TEXT", nullable: true),
                    DataType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    DefaultValue = table.Column<string>(type: "TEXT", nullable: true),
                    Category = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Subcategory = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Description = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Application = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    ModuleName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    ComponentName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    Scope = table.Column<string>(type: "TEXT", maxLength: 50, nullable: false),
                    MachineName = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    Environment = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    FrameworkVersion = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    ApplicationVersion = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    UserDisplayName = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    UserEmail = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    UserRole = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    DeviceId = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    DeviceType = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
                    OperatingSystem = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
                    BrowserInfo = table.Column<string>(type: "TEXT", maxLength: 200, nullable: true),
                    Tags = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: true),
                    Notes = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: true),
                    MetadataJson = table.Column<string>(type: "TEXT", nullable: true),
                    ChangeHistoryJson = table.Column<string>(type: "TEXT", nullable: true),
                    UsageStatisticsJson = table.Column<string>(type: "TEXT", nullable: true),
                    CreatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    UpdatedAt = table.Column<string>(type: "TEXT", nullable: false),
                    IsActive = table.Column<bool>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserPreferences", x => x.Id);
                });

            // Create indexes for TestExecutionResults
            migrationBuilder.CreateIndex(
                name: "IX_TestExecutionResults_TestName",
                table: "TestExecutionResults",
                column: "TestName");

            migrationBuilder.CreateIndex(
                name: "IX_TestExecutionResults_TestSuite",
                table: "TestExecutionResults",
                column: "TestSuite");

            migrationBuilder.CreateIndex(
                name: "IX_TestExecutionResults_Passed",
                table: "TestExecutionResults",
                column: "Passed");

            migrationBuilder.CreateIndex(
                name: "IX_TestExecutionResults_CreatedAt",
                table: "TestExecutionResults",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_TestExecutionResults_SessionId",
                table: "TestExecutionResults",
                column: "SessionId");

            migrationBuilder.CreateIndex(
                name: "IX_TestExecutionResults_TestSuite_TestName_CreatedAt",
                table: "TestExecutionResults",
                columns: new[] { "TestSuite", "TestName", "CreatedAt" });

            // Create indexes for PerformanceBenchmarks
            migrationBuilder.CreateIndex(
                name: "IX_PerformanceBenchmarks_OperationName",
                table: "PerformanceBenchmarks",
                column: "OperationName");

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceBenchmarks_Category",
                table: "PerformanceBenchmarks",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceBenchmarks_CreatedAt",
                table: "PerformanceBenchmarks",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceBenchmarks_SessionId",
                table: "PerformanceBenchmarks",
                column: "SessionId");

            migrationBuilder.CreateIndex(
                name: "IX_PerformanceBenchmarks_Category_OperationName_CreatedAt",
                table: "PerformanceBenchmarks",
                columns: new[] { "Category", "OperationName", "CreatedAt" });

            // Create indexes for ModuleHealthStatuses
            migrationBuilder.CreateIndex(
                name: "IX_ModuleHealthStatuses_ModuleName",
                table: "ModuleHealthStatuses",
                column: "ModuleName");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleHealthStatuses_CurrentStatus",
                table: "ModuleHealthStatuses",
                column: "CurrentStatus");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleHealthStatuses_CreatedAt",
                table: "ModuleHealthStatuses",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleHealthStatuses_SessionId",
                table: "ModuleHealthStatuses",
                column: "SessionId");

            migrationBuilder.CreateIndex(
                name: "IX_ModuleHealthStatuses_ModuleName_CreatedAt",
                table: "ModuleHealthStatuses",
                columns: new[] { "ModuleName", "CreatedAt" });

            // Create indexes for FrameworkConfigurations
            migrationBuilder.CreateIndex(
                name: "IX_FrameworkConfigurations_ConfigurationKey",
                table: "FrameworkConfigurations",
                column: "ConfigurationKey",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FrameworkConfigurations_Category",
                table: "FrameworkConfigurations",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_FrameworkConfigurations_Scope",
                table: "FrameworkConfigurations",
                column: "Scope");

            migrationBuilder.CreateIndex(
                name: "IX_FrameworkConfigurations_Environment",
                table: "FrameworkConfigurations",
                column: "Environment");

            migrationBuilder.CreateIndex(
                name: "IX_FrameworkConfigurations_IsActive",
                table: "FrameworkConfigurations",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_FrameworkConfigurations_Category_ConfigurationKey",
                table: "FrameworkConfigurations",
                columns: new[] { "Category", "ConfigurationKey" });

            migrationBuilder.CreateIndex(
                name: "IX_FrameworkConfigurations_Scope_Environment_ConfigurationKey",
                table: "FrameworkConfigurations",
                columns: new[] { "Scope", "Environment", "ConfigurationKey" });

            // Create indexes for UserPreferences
            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_UserId",
                table: "UserPreferences",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_PreferenceKey",
                table: "UserPreferences",
                column: "PreferenceKey");

            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_Application",
                table: "UserPreferences",
                column: "Application");

            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_Category",
                table: "UserPreferences",
                column: "Category");

            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_IsActive",
                table: "UserPreferences",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_UserId_PreferenceKey",
                table: "UserPreferences",
                columns: new[] { "UserId", "PreferenceKey" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserPreferences_Application_UserId_Category",
                table: "UserPreferences",
                columns: new[] { "Application", "UserId", "Category" });
        }

        /// <summary>
        /// Drops the database schema
        /// </summary>
        /// <param name="migrationBuilder">Migration builder</param>
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(name: "TestExecutionResults");
            migrationBuilder.DropTable(name: "PerformanceBenchmarks");
            migrationBuilder.DropTable(name: "ModuleHealthStatuses");
            migrationBuilder.DropTable(name: "FrameworkConfigurations");
            migrationBuilder.DropTable(name: "UserPreferences");
        }
    }
}
