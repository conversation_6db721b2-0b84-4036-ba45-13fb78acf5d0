using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using ArtDesignFramework.DataAccess.Entities;
using ArtDesignFramework.DataAccess.Repositories;
using ArtDesignFramework.TestFramework;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ArtDesignFramework.TestFramework.Core
{
    /// <summary>
    /// Enterprise-grade module health monitoring and validation system
    /// </summary>
    public class ModuleHealthMonitor
    {
        private readonly ILogger<ModuleHealthMonitor> _logger;
        private readonly Dictionary<string, ModuleHealthStatus> _moduleStatuses;
        private readonly string _healthReportPath;
        private readonly IModuleHealthRepository? _moduleHealthRepository;
        private readonly TestFrameworkOptions _options;

        /// <summary>
        /// Initializes a new instance of the ModuleHealthMonitor class
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="options">TestFramework options</param>
        /// <param name="moduleHealthRepository">Optional module health repository for database storage</param>
        public ModuleHealthMonitor(
            ILogger<ModuleHealthMonitor> logger,
            IOptions<TestFrameworkOptions>? options = null,
            IModuleHealthRepository? moduleHealthRepository = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _moduleStatuses = new Dictionary<string, ModuleHealthStatus>();
            _healthReportPath = Path.Combine("TestResults", "ModuleHealth");
            Directory.CreateDirectory(_healthReportPath);
            _options = options?.Value ?? new TestFrameworkOptions();
            _moduleHealthRepository = moduleHealthRepository;
        }

        /// <summary>
        /// Validates module status claims against actual implementation
        /// </summary>
        /// <param name="modulePath">Path to the module directory</param>
        /// <returns>Module health validation result</returns>
        public async Task<ModuleHealthValidationResult> ValidateModuleHealthAsync(string modulePath)
        {
            var result = new ModuleHealthValidationResult
            {
                ModuleName = Path.GetFileName(modulePath),
                ValidationTimestamp = DateTime.UtcNow,
                ValidationPassed = true
            };

            try
            {
                _logger.LogInformation("Validating module health for: {ModuleName}", result.ModuleName);

                // Check if project file exists
                var projectFiles = Directory.GetFiles(modulePath, "*.csproj", SearchOption.TopDirectoryOnly);
                if (projectFiles.Length == 0)
                {
                    result.Issues.Add(new HealthIssue
                    {
                        Severity = IssueSeverity.Critical,
                        Category = "Project Structure",
                        Description = "No .csproj file found - module cannot be built",
                        Recommendation = "Create a proper .csproj file for the module"
                    });
                    result.ValidationPassed = false;
                }

                // Check for actual implementation files
                var sourceFiles = Directory.GetFiles(modulePath, "*.cs", SearchOption.AllDirectories)
                    .Where(f => !f.Contains("bin") && !f.Contains("obj"))
                    .ToList();

                if (sourceFiles.Count == 0)
                {
                    result.Issues.Add(new HealthIssue
                    {
                        Severity = IssueSeverity.Critical,
                        Category = "Implementation",
                        Description = "No source files found - module appears to be empty",
                        Recommendation = "Implement actual module functionality"
                    });
                    result.ValidationPassed = false;
                }

                // Validate build status if project file exists
                if (projectFiles.Length > 0)
                {
                    var buildResult = await ValidateBuildStatusAsync(projectFiles[0]);
                    result.BuildValidation = buildResult;
                    if (!buildResult.BuildSuccessful)
                    {
                        result.ValidationPassed = false;
                    }
                }

                // Check for test files
                var testFiles = Directory.GetFiles(modulePath, "*Test*.cs", SearchOption.AllDirectories)
                    .Where(f => !f.Contains("bin") && !f.Contains("obj"))
                    .ToList();

                result.TestCoverage = new TestCoverageInfo
                {
                    HasTests = testFiles.Count > 0,
                    TestFileCount = testFiles.Count,
                    SourceFileCount = sourceFiles.Count,
                    CoverageRatio = sourceFiles.Count > 0 ? (double)testFiles.Count / sourceFiles.Count : 0
                };

                // Validate claimed status vs actual status
                await ValidateStatusClaimsAsync(result);

                _logger.LogInformation("Module health validation completed for {ModuleName}: {Status}",
                    result.ModuleName, result.ValidationPassed ? "PASSED" : "FAILED");

                // Store health data in database if enabled
                await StoreModuleHealthDataAsync(result);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating module health for {ModuleName}", result.ModuleName);
                result.ValidationPassed = false;
                result.Issues.Add(new HealthIssue
                {
                    Severity = IssueSeverity.Critical,
                    Category = "Validation Error",
                    Description = $"Validation failed with exception: {ex.Message}",
                    Recommendation = "Review module structure and fix underlying issues"
                });
                return result;
            }
        }

        /// <summary>
        /// Stores module health data in the database if database storage is enabled
        /// </summary>
        /// <param name="result">Module health validation result</param>
        /// <returns>Task representing the storage operation</returns>
        private async Task StoreModuleHealthDataAsync(ModuleHealthValidationResult result)
        {
            if (!_options.EnableDatabaseStorage || _moduleHealthRepository == null)
            {
                _logger.LogDebug("Database storage disabled or repository not available for module {ModuleName}", result.ModuleName);
                return;
            }

            try
            {
                _logger.LogDebug("💾 Storing module health data for {ModuleName}", result.ModuleName);

                var healthStatus = new ArtDesignFramework.DataAccess.Entities.ModuleHealthStatus
                {
                    ModuleName = result.ModuleName,
                    Version = "2.0.0", // Could be extracted from project file
                    CurrentStatus = result.ValidationPassed ? "Healthy" : "Issues",
                    PreviousStatus = null, // Could be retrieved from previous records
                    ValidatedAt = result.ValidationTimestamp,
                    ClaimedStatus = result.ClaimedStatus?.Status ?? "Unknown",
                    ValidationIssuesJson = result.Issues?.Any() == true
                        ? JsonSerializer.Serialize(result.Issues)
                        : null,
                    RecommendationsJson = result.Issues?.Any() == true
                        ? JsonSerializer.Serialize(result.Issues.Select(i => i.Recommendation).Where(r => !string.IsNullOrEmpty(r)))
                        : null,
                    DependenciesJson = null, // Could be extracted from project file
                    PerformanceMetricsJson = result.TestCoverage != null
                        ? JsonSerializer.Serialize(new
                        {
                            TestFileCount = result.TestCoverage.TestFileCount,
                            SourceFileCount = result.TestCoverage.SourceFileCount,
                            CoverageRatio = result.TestCoverage.CoverageRatio,
                            HasTests = result.TestCoverage.HasTests
                        })
                        : null,
                    SecurityScanResultsJson = null, // Could be added in future
                    SessionId = Guid.NewGuid(), // Could be passed from orchestrator
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                    MachineName = Environment.MachineName,
                    FrameworkVersion = "2.0.0",
                    BuildVersion = "2.0.0",
                    Tags = $"health,module,{result.ModuleName.ToLowerInvariant()}",
                    Notes = $"Health validation for module {result.ModuleName} - {(result.ValidationPassed ? "Passed" : "Failed")}"
                };

                await _moduleHealthRepository.AddAsync(healthStatus);

                _logger.LogDebug("✅ Module health data stored for {ModuleName}", result.ModuleName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Failed to store module health data for {ModuleName}", result.ModuleName);
                // Don't throw - database storage failure shouldn't break health monitoring
            }
        }

        /// <summary>
        /// Validates build status of a project
        /// </summary>
        /// <param name="projectPath">Path to the .csproj file</param>
        /// <returns>Build validation result</returns>
        private async Task<BuildValidationResult> ValidateBuildStatusAsync(string projectPath)
        {
            var result = new BuildValidationResult
            {
                ProjectPath = projectPath,
                BuildSuccessful = false,
                Warnings = new List<string>(),
                Errors = new List<string>()
            };

            try
            {
                _logger.LogInformation("Validating build status for project: {ProjectPath}", projectPath);

                // Implement actual build validation using Process.Start with dotnet build
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = "dotnet",
                    Arguments = $"build \"{projectPath}\" --configuration Release --verbosity minimal --no-restore",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    WorkingDirectory = Path.GetDirectoryName(projectPath) ?? Environment.CurrentDirectory
                };

                using var process = new Process { StartInfo = processStartInfo };
                var outputBuilder = new StringBuilder();
                var errorBuilder = new StringBuilder();

                process.OutputDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                        outputBuilder.AppendLine(e.Data);
                };

                process.ErrorDataReceived += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(e.Data))
                        errorBuilder.AppendLine(e.Data);
                };

                process.Start();
                process.BeginOutputReadLine();
                process.BeginErrorReadLine();

                // Wait for completion with timeout
                var completed = await Task.Run(() => process.WaitForExit(30000));

                if (!completed)
                {
                    process.Kill();
                    result.Errors.Add("Build validation timed out after 30 seconds");
                    result.BuildSuccessful = false;
                }
                else
                {
                    result.BuildSuccessful = process.ExitCode == 0;

                    if (!result.BuildSuccessful)
                    {
                        var errorOutput = errorBuilder.ToString();
                        if (!string.IsNullOrEmpty(errorOutput))
                        {
                            result.Errors.Add($"Build failed: {errorOutput}");
                        }

                        var output = outputBuilder.ToString();
                        if (!string.IsNullOrEmpty(output))
                        {
                            result.Errors.Add($"Build output: {output}");
                        }
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating build status for {ProjectPath}", projectPath);
                result.Errors.Add($"Build validation failed: {ex.Message}");
                return result;
            }
        }

        /// <summary>
        /// Validates status claims against actual implementation
        /// </summary>
        /// <param name="result">Module health validation result to update</param>
        /// <returns>Task representing the validation operation</returns>
        private async Task ValidateStatusClaimsAsync(ModuleHealthValidationResult result)
        {
            // Load claimed status from knowledge base
            var knowledgeBasePath = Path.Combine("modules", "FRAMEWORK_AI_KNOWLEDGE.json");
            if (File.Exists(knowledgeBasePath))
            {
                try
                {
                    var knowledgeJson = await File.ReadAllTextAsync(knowledgeBasePath);
                    var knowledgeData = JsonDocument.Parse(knowledgeJson);

                    // Extract claimed status for this module
                    // This is a simplified version - would need more sophisticated parsing
                    var claimedStatus = ExtractClaimedStatus(knowledgeData, result.ModuleName);

                    if (claimedStatus != null)
                    {
                        result.ClaimedStatus = claimedStatus;

                        // Validate claims against reality
                        if (claimedStatus.Status == "production_ready" && !result.ValidationPassed)
                        {
                            result.Issues.Add(new HealthIssue
                            {
                                Severity = IssueSeverity.Critical,
                                Category = "Status Mismatch",
                                Description = "Module claims 'production_ready' status but validation failed",
                                Recommendation = "Fix implementation issues or update status claims"
                            });
                        }

                        if (claimedStatus.TestResults != null && result.TestCoverage?.HasTests == false)
                        {
                            result.Issues.Add(new HealthIssue
                            {
                                Severity = IssueSeverity.High,
                                Category = "Test Claims",
                                Description = "Module claims test results but no test files found",
                                Recommendation = "Implement actual tests or remove phantom test claims"
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Could not validate status claims for {ModuleName}", result.ModuleName);
                }
            }
        }

        /// <summary>
        /// Extracts claimed status from knowledge base data
        /// </summary>
        /// <param name="knowledgeData">Knowledge base JSON document</param>
        /// <param name="moduleName">Name of the module</param>
        /// <returns>Claimed status information</returns>
        private ClaimedModuleStatus? ExtractClaimedStatus(JsonDocument knowledgeData, string moduleName)
        {
            // Simplified extraction - would need more sophisticated implementation
            return new ClaimedModuleStatus
            {
                Status = "unknown",
                Version = "unknown",
                TestResults = null
            };
        }

        /// <summary>
        /// Generates a comprehensive health dashboard
        /// </summary>
        /// <param name="validationResults">List of module validation results</param>
        /// <returns>Task representing the dashboard generation</returns>
        public async Task GenerateHealthDashboardAsync(List<ModuleHealthValidationResult> validationResults)
        {
            var dashboard = new HealthDashboard
            {
                GeneratedAt = DateTime.UtcNow,
                TotalModules = validationResults.Count,
                HealthyModules = validationResults.Count(r => r.ValidationPassed),
                CriticalIssues = validationResults.SelectMany(r => r.Issues)
                    .Count(i => i.Severity == IssueSeverity.Critical),
                ModuleResults = validationResults
            };

            var dashboardJson = JsonSerializer.Serialize(dashboard, new JsonSerializerOptions
            {
                WriteIndented = true
            });

            var dashboardPath = Path.Combine(_healthReportPath, $"HealthDashboard_{DateTime.UtcNow:yyyyMMdd_HHmmss}.json");
            await File.WriteAllTextAsync(dashboardPath, dashboardJson);

            // Generate HTML dashboard
            await GenerateHtmlDashboardAsync(dashboard, dashboardPath.Replace(".json", ".html"));

            _logger.LogInformation("Health dashboard generated: {DashboardPath}", dashboardPath);
        }

        /// <summary>
        /// Generates an HTML health dashboard
        /// </summary>
        /// <param name="dashboard">Dashboard data</param>
        /// <param name="outputPath">Output HTML file path</param>
        /// <returns>Task representing the HTML generation</returns>
        private async Task GenerateHtmlDashboardAsync(HealthDashboard dashboard, string outputPath)
        {
            var html = GenerateHtmlDashboardContent(dashboard);
            await File.WriteAllTextAsync(outputPath, html);
        }

        /// <summary>
        /// Generates HTML content for the health dashboard
        /// </summary>
        /// <param name="dashboard">Dashboard data</param>
        /// <returns>HTML content string</returns>
        private string GenerateHtmlDashboardContent(HealthDashboard dashboard)
        {
            var healthPercentage = dashboard.TotalModules > 0
                ? (double)dashboard.HealthyModules / dashboard.TotalModules * 100
                : 0;

            return $@"<!DOCTYPE html>
<html>
<head>
    <title>ArtDesignFramework - Module Health Dashboard</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }}
        .metrics {{ display: flex; gap: 20px; margin: 20px 0; }}
        .metric {{ background: #ecf0f1; padding: 15px; border-radius: 5px; flex: 1; text-align: center; }}
        .healthy {{ background: #2ecc71; color: white; }}
        .warning {{ background: #f39c12; color: white; }}
        .critical {{ background: #e74c3c; color: white; }}
        .module {{ margin: 10px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .issue {{ margin: 5px 0; padding: 10px; border-left: 4px solid #e74c3c; background: #fdf2f2; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>🏥 ArtDesignFramework Module Health Dashboard</h1>
        <p>Generated: {dashboard.GeneratedAt:yyyy-MM-dd HH:mm:ss} UTC</p>
    </div>

    <div class='metrics'>
        <div class='metric {(healthPercentage >= 80 ? "healthy" : healthPercentage >= 60 ? "warning" : "critical")}'>
            <h3>Overall Health</h3>
            <h2>{healthPercentage:F1}%</h2>
        </div>
        <div class='metric'>
            <h3>Total Modules</h3>
            <h2>{dashboard.TotalModules}</h2>
        </div>
        <div class='metric healthy'>
            <h3>Healthy Modules</h3>
            <h2>{dashboard.HealthyModules}</h2>
        </div>
        <div class='metric critical'>
            <h3>Critical Issues</h3>
            <h2>{dashboard.CriticalIssues}</h2>
        </div>
    </div>

    <h2>Module Details</h2>
    {string.Join("", dashboard.ModuleResults.Select(GenerateModuleHtml))}
</body>
</html>";
        }

        /// <summary>
        /// Generates HTML for a single module
        /// </summary>
        /// <param name="module">Module validation result</param>
        /// <returns>HTML string for the module</returns>
        private string GenerateModuleHtml(ModuleHealthValidationResult module)
        {
            var statusClass = module.ValidationPassed ? "healthy" : "critical";
            var statusIcon = module.ValidationPassed ? "✅" : "❌";

            var issuesHtml = string.Join("", module.Issues.Select(issue =>
                $"<div class='issue'><strong>{issue.Category}:</strong> {issue.Description}</div>"));

            return $@"
    <div class='module'>
        <h3>{statusIcon} {module.ModuleName}</h3>
        <p><strong>Status:</strong> {(module.ValidationPassed ? "Healthy" : "Issues Found")}</p>
        <p><strong>Validated:</strong> {module.ValidationTimestamp:yyyy-MM-dd HH:mm:ss}</p>
        {(module.Issues.Any() ? $"<h4>Issues:</h4>{issuesHtml}" : "")}
    </div>";
        }
    }
}
