// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArtDesignFramework.DataAccess.Entities;

/// <summary>
/// Entity representing performance benchmarking data and metrics
/// </summary>
[Table("PerformanceBenchmarks")]
public class PerformanceBenchmark
{
    /// <summary>
    /// Gets or sets the unique identifier for the performance benchmark
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Gets or sets the name of the operation being benchmarked
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string OperationName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the category of the performance benchmark
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the subcategory for more granular classification
    /// </summary>
    [MaxLength(200)]
    public string? Subcategory { get; set; }

    /// <summary>
    /// Gets or sets the execution time in milliseconds
    /// </summary>
    public double ExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the memory usage in bytes
    /// </summary>
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the peak memory usage in bytes
    /// </summary>
    public long PeakMemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the CPU usage percentage
    /// </summary>
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// Gets or sets the GPU usage percentage if applicable
    /// </summary>
    public double? GpuUsagePercent { get; set; }

    /// <summary>
    /// Gets or sets the GPU memory usage in bytes if applicable
    /// </summary>
    public long? GpuMemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the throughput (operations per second)
    /// </summary>
    public double? ThroughputOps { get; set; }

    /// <summary>
    /// Gets or sets the number of operations performed
    /// </summary>
    public long OperationCount { get; set; }

    /// <summary>
    /// Gets or sets the baseline value for comparison
    /// </summary>
    public double? BaselineValue { get; set; }

    /// <summary>
    /// Gets or sets the target improvement percentage
    /// </summary>
    public double? TargetImprovement { get; set; }

    /// <summary>
    /// Gets or sets the actual improvement percentage achieved
    /// </summary>
    public double? ActualImprovement { get; set; }

    /// <summary>
    /// Gets or sets whether the benchmark met its performance target
    /// </summary>
    public bool? MetTarget { get; set; }

    /// <summary>
    /// Gets or sets the unit of measurement for the primary metric
    /// </summary>
    [MaxLength(50)]
    public string Unit { get; set; } = "ms";

    /// <summary>
    /// Gets or sets the benchmark description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the test data size or complexity
    /// </summary>
    [MaxLength(200)]
    public string? TestDataSize { get; set; }

    /// <summary>
    /// Gets or sets the rendering quality level if applicable
    /// </summary>
    [MaxLength(100)]
    public string? RenderingQuality { get; set; }

    /// <summary>
    /// Gets or sets the number of concurrent operations
    /// </summary>
    public int? ConcurrencyLevel { get; set; }

    /// <summary>
    /// Gets or sets the thread count used
    /// </summary>
    public int? ThreadCount { get; set; }

    /// <summary>
    /// Gets or sets the frame rate if applicable (for rendering benchmarks)
    /// </summary>
    public double? FrameRate { get; set; }

    /// <summary>
    /// Gets or sets the number of garbage collections during the benchmark
    /// </summary>
    public int? GarbageCollections { get; set; }

    /// <summary>
    /// Gets or sets the allocated memory that was garbage collected
    /// </summary>
    public long? GcMemoryBytes { get; set; }

    /// <summary>
    /// Gets or sets custom performance metrics as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? CustomMetricsJson { get; set; }

    /// <summary>
    /// Gets or sets the environment where the benchmark was executed
    /// </summary>
    [MaxLength(100)]
    public string Environment { get; set; } = "Development";

    /// <summary>
    /// Gets or sets the machine name where the benchmark was executed
    /// </summary>
    [MaxLength(100)]
    public string MachineName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the framework version used during benchmarking
    /// </summary>
    [MaxLength(50)]
    public string FrameworkVersion { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the build version or commit hash
    /// </summary>
    [MaxLength(100)]
    public string? BuildVersion { get; set; }

    /// <summary>
    /// Gets or sets the benchmark session identifier
    /// </summary>
    public Guid? SessionId { get; set; }

    /// <summary>
    /// Gets or sets when the benchmark was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the benchmark was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the benchmark started
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// Gets or sets when the benchmark completed
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Gets or sets whether this is a regression benchmark
    /// </summary>
    public bool IsRegressionBenchmark { get; set; }

    /// <summary>
    /// Gets or sets whether this benchmark is part of automated testing
    /// </summary>
    public bool IsAutomatedBenchmark { get; set; }

    /// <summary>
    /// Gets or sets tags associated with this benchmark
    /// </summary>
    [MaxLength(1000)]
    public string? Tags { get; set; }

    /// <summary>
    /// Gets or sets additional notes or comments
    /// </summary>
    [MaxLength(2000)]
    public string? Notes { get; set; }
}
