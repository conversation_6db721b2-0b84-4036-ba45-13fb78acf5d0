// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArtDesignFramework.DataAccess.Entities;

/// <summary>
/// Entity representing module health monitoring data and validation results
/// </summary>
[Table("ModuleHealthStatuses")]
public class ModuleHealthStatus
{
    /// <summary>
    /// Gets or sets the unique identifier for the module health status
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Gets or sets the module name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string ModuleName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the module version
    /// </summary>
    [MaxLength(50)]
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the current health status
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string CurrentStatus { get; set; } = "Unknown";

    /// <summary>
    /// Gets or sets the health score (0.0 to 1.0)
    /// </summary>
    public double HealthScore { get; set; } = 1.0;

    /// <summary>
    /// Gets or sets the previous health status for comparison
    /// </summary>
    [MaxLength(50)]
    public string? PreviousStatus { get; set; }

    /// <summary>
    /// Gets or sets the previous health score for trend analysis
    /// </summary>
    public double? PreviousHealthScore { get; set; }

    /// <summary>
    /// Gets or sets whether the module builds successfully
    /// </summary>
    public bool BuildsSuccessfully { get; set; }

    /// <summary>
    /// Gets or sets the build time in milliseconds
    /// </summary>
    public double? BuildTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the number of build warnings
    /// </summary>
    public int BuildWarnings { get; set; }

    /// <summary>
    /// Gets or sets the number of build errors
    /// </summary>
    public int BuildErrors { get; set; }

    /// <summary>
    /// Gets or sets the test coverage percentage
    /// </summary>
    public double? TestCoverage { get; set; }

    /// <summary>
    /// Gets or sets the number of passing tests
    /// </summary>
    public int PassingTests { get; set; }

    /// <summary>
    /// Gets or sets the number of failing tests
    /// </summary>
    public int FailingTests { get; set; }

    /// <summary>
    /// Gets or sets the total number of tests
    /// </summary>
    public int TotalTests { get; set; }

    /// <summary>
    /// Gets or sets whether phantom implementations were detected
    /// </summary>
    public bool HasPhantomImplementations { get; set; }

    /// <summary>
    /// Gets or sets the number of phantom implementations found
    /// </summary>
    public int PhantomImplementationCount { get; set; }

    /// <summary>
    /// Gets or sets whether the module has proper documentation
    /// </summary>
    public bool HasDocumentation { get; set; }

    /// <summary>
    /// Gets or sets the documentation coverage percentage
    /// </summary>
    public double? DocumentationCoverage { get; set; }

    /// <summary>
    /// Gets or sets whether the module follows coding standards
    /// </summary>
    public bool FollowsCodingStandards { get; set; }

    /// <summary>
    /// Gets or sets the number of code quality issues
    /// </summary>
    public int CodeQualityIssues { get; set; }

    /// <summary>
    /// Gets or sets the claimed module status from documentation
    /// </summary>
    [MaxLength(100)]
    public string? ClaimedStatus { get; set; }

    /// <summary>
    /// Gets or sets whether the claimed status matches actual status
    /// </summary>
    public bool? StatusClaimAccurate { get; set; }

    /// <summary>
    /// Gets or sets the number of critical issues found
    /// </summary>
    public int CriticalIssues { get; set; }

    /// <summary>
    /// Gets or sets the number of major issues found
    /// </summary>
    public int MajorIssues { get; set; }

    /// <summary>
    /// Gets or sets the number of minor issues found
    /// </summary>
    public int MinorIssues { get; set; }

    /// <summary>
    /// Gets or sets the validation issues as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? ValidationIssuesJson { get; set; }

    /// <summary>
    /// Gets or sets the validation recommendations as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? RecommendationsJson { get; set; }

    /// <summary>
    /// Gets or sets the module dependencies as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? DependenciesJson { get; set; }

    /// <summary>
    /// Gets or sets the performance metrics as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? PerformanceMetricsJson { get; set; }

    /// <summary>
    /// Gets or sets the security scan results as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? SecurityScanResultsJson { get; set; }

    /// <summary>
    /// Gets or sets the environment where the validation was performed
    /// </summary>
    [MaxLength(100)]
    public string Environment { get; set; } = "Development";

    /// <summary>
    /// Gets or sets the machine name where the validation was performed
    /// </summary>
    [MaxLength(100)]
    public string MachineName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the framework version used during validation
    /// </summary>
    [MaxLength(50)]
    public string FrameworkVersion { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the build version or commit hash
    /// </summary>
    [MaxLength(100)]
    public string? BuildVersion { get; set; }

    /// <summary>
    /// Gets or sets the validation session identifier
    /// </summary>
    public Guid? SessionId { get; set; }

    /// <summary>
    /// Gets or sets when the health status was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the health status was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the validation was performed
    /// </summary>
    public DateTime? ValidatedAt { get; set; }

    /// <summary>
    /// Gets or sets when the next validation is scheduled
    /// </summary>
    public DateTime? NextValidationAt { get; set; }

    /// <summary>
    /// Gets or sets whether this is an automated validation
    /// </summary>
    public bool IsAutomatedValidation { get; set; }

    /// <summary>
    /// Gets or sets tags associated with this health status
    /// </summary>
    [MaxLength(1000)]
    public string? Tags { get; set; }

    /// <summary>
    /// Gets or sets additional notes or comments
    /// </summary>
    [MaxLength(2000)]
    public string? Notes { get; set; }
}
