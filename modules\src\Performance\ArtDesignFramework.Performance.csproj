<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- Performance module specific settings -->
    <AssemblyTitle>ArtDesignFramework Performance Module</AssemblyTitle>
    <AssemblyDescription>Hardware acceleration and performance optimization for the ArtDesignFramework</AssemblyDescription>
  </PropertyGroup>

  <ItemGroup>
    <!-- Module-specific packages not provided by Directory.Build.props -->
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.0" />
    <PackageReference Include="System.Diagnostics.PerformanceCounter" Version="9.0.0" />
    <PackageReference Include="System.Management" Version="9.0.0" />
    <PackageReference Include="System.Reactive" Version="6.0.0" />
    <PackageReference Include="System.Threading.Channels" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\ArtDesignFramework.Core.csproj" />
    <ProjectReference Include="..\Utilities\ArtDesignFramework.Utilities.csproj" />
    <ProjectReference Include="..\DataAccess\ArtDesignFramework.DataAccess.csproj" />
  </ItemGroup>

</Project>
