# ArtDesignFramework Module Development Standards

**Last Updated:** 2025-06-08 21:30:00 UTC

## Overview
This document defines the standards and patterns for creating new modules in the ArtDesignFramework to ensure consistency with the established consolidated structure.

## Module Structure Requirements

### 1. Directory Structure
```
modules/src/[ModuleName]/
├── ArtDesignFramework.[ModuleName].csproj
├── [ModuleName]Service.cs
├── I[ModuleName]Service.cs
├── Models/
├── Services/
└── Extensions/
```

### 2. Project File Template
```xml
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- [ModuleName] module specific settings -->
    <AssemblyTitle>ArtDesignFramework [ModuleName] Module</AssemblyTitle>
    <AssemblyDescription>[Brief description of module functionality]</AssemblyDescription>
  </PropertyGroup>

  <ItemGroup>
    <!-- Module-specific packages not provided by Directory.Build.props -->
    <!-- Add only packages unique to this module -->
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\ArtDesignFramework.Core.csproj" />
    <!-- Add other module dependencies as needed -->
  </ItemGroup>

</Project>
```

### 3. Inherited Configuration
All modules automatically inherit from `Directory.Build.props`:
- TargetFramework: net9.0
- LangVersion: latest
- Nullable: enable
- ImplicitUsings: enable
- GenerateDocumentationFile: true
- TreatWarningsAsErrors: true
- Common packages: Microsoft.Extensions.*, System.Text.Json
- Graphics packages: SkiaSharp (excluded for Performance and Utilities)

### 4. Module Categories and Solution Folders

#### Core Modules (********-0000-0000-0000-000000000000)
- Core, TestFramework, Utilities
- Foundation modules used by other modules

#### Graphics Modules (********-1111-1111-1111-111111111111)
- EffectsEngine, FontRendering, ImageHandling, ThemingEngine, UserInterface, VectorGraphics
- Modules that work with visual content and rendering

#### System Modules (********-**************-************)
- Performance, PluginSystem, DataAccess
- Infrastructure and system-level modules

### 5. Adding a New Module

#### Step 1: Create Module Project
1. Create directory: `modules/src/[ModuleName]/`
2. Create project file using template above
3. Implement core interfaces and services

#### Step 2: Update Solution File
1. Add project entry to `ArtDesignFramework.sln`
2. Assign appropriate solution folder GUID
3. Add configuration mappings for Debug/Release
4. Add to NestedProjects section

#### Step 3: Create Test Project (Recommended)
1. Create directory: `modules/tests/ArtDesignFramework.[ModuleName].Tests/`
2. Use test project template (inherits test packages from Directory.Build.props)
3. Add to solution under Tests folder

### 6. Code Quality Standards
- Follow established warning suppression patterns
- Use ArtDesignFramework.ruleset for code analysis
- Implement XML documentation for public APIs
- Follow dependency injection patterns from Core module

### 7. Package Reference Guidelines
- Avoid duplicating packages provided by Directory.Build.props
- Use latest stable versions compatible with .NET 9.0
- Document any special package requirements in module README

## Example: Adding a "DataAccess" Module

### Project File
```xml
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <AssemblyTitle>ArtDesignFramework DataAccess Module</AssemblyTitle>
    <AssemblyDescription>Data access and persistence for the ArtDesignFramework</AssemblyDescription>
  </PropertyGroup>
  
  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
  </ItemGroup>
  
  <ItemGroup>
    <ProjectReference Include="..\Core\ArtDesignFramework.Core.csproj" />
  </ItemGroup>
</Project>
```

### Solution Entry
```
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArtDesignFramework.DataAccess", "src\DataAccess\ArtDesignFramework.DataAccess.csproj", "{NEW-GUID-HERE}"
EndProject
```

This ensures consistency with the established patterns and maintains the clean, organized structure of the ArtDesignFramework.
