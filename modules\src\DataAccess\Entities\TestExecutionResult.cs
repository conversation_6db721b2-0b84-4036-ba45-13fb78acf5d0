// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArtDesignFramework.DataAccess.Entities;

/// <summary>
/// Entity representing test execution results and metrics for the TestFramework
/// </summary>
[Table("TestExecutionResults")]
public class TestExecutionResult
{
    /// <summary>
    /// Gets or sets the unique identifier for the test execution result
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Gets or sets the name of the test that was executed
    /// </summary>
    [Required]
    [MaxLength(500)]
    public string TestName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the test suite or module name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string TestSuite { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether the test passed
    /// </summary>
    public bool Passed { get; set; }

    /// <summary>
    /// Gets or sets the execution time in milliseconds
    /// </summary>
    public double ExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the memory usage in bytes during test execution
    /// </summary>
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the CPU usage percentage during test execution
    /// </summary>
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// Gets or sets the error message if the test failed
    /// </summary>
    [MaxLength(2000)]
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the full error details including stack trace
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? ErrorDetails { get; set; }

    /// <summary>
    /// Gets or sets the test execution code that was run
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? TestCode { get; set; }

    /// <summary>
    /// Gets or sets the test parameters as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? TestParametersJson { get; set; }

    /// <summary>
    /// Gets or sets additional context about the test execution as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? ContextJson { get; set; }

    /// <summary>
    /// Gets or sets the test category (e.g., Unit, Integration, Performance)
    /// </summary>
    [MaxLength(100)]
    public string TestCategory { get; set; } = "Unit";

    /// <summary>
    /// Gets or sets the test priority level
    /// </summary>
    [MaxLength(50)]
    public string Priority { get; set; } = "Normal";

    /// <summary>
    /// Gets or sets the environment where the test was executed
    /// </summary>
    [MaxLength(100)]
    public string Environment { get; set; } = "Development";

    /// <summary>
    /// Gets or sets the machine name where the test was executed
    /// </summary>
    [MaxLength(100)]
    public string MachineName { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the framework version used during test execution
    /// </summary>
    [MaxLength(50)]
    public string FrameworkVersion { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the test runner version
    /// </summary>
    [MaxLength(50)]
    public string TestRunnerVersion { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the build number or commit hash
    /// </summary>
    [MaxLength(100)]
    public string? BuildVersion { get; set; }

    /// <summary>
    /// Gets or sets the test execution session identifier
    /// </summary>
    public Guid? SessionId { get; set; }

    /// <summary>
    /// Gets or sets when the test execution was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the test execution was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the test execution started
    /// </summary>
    public DateTime? StartedAt { get; set; }

    /// <summary>
    /// Gets or sets when the test execution completed
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Gets or sets whether this test result is part of a regression test
    /// </summary>
    public bool IsRegressionTest { get; set; }

    /// <summary>
    /// Gets or sets whether this test result is from an automated build
    /// </summary>
    public bool IsAutomatedBuild { get; set; }

    /// <summary>
    /// Gets or sets the test coverage percentage if available
    /// </summary>
    public double? CodeCoverage { get; set; }

    /// <summary>
    /// Gets or sets custom metrics as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? CustomMetricsJson { get; set; }

    /// <summary>
    /// Gets or sets tags associated with this test execution
    /// </summary>
    [MaxLength(1000)]
    public string? Tags { get; set; }

    /// <summary>
    /// Gets or sets additional notes or comments
    /// </summary>
    [MaxLength(2000)]
    public string? Notes { get; set; }
}
