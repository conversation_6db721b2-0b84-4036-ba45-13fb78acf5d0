// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Configuration;
using ArtDesignFramework.DataAccess.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository implementation for test execution result data access
/// </summary>
public class TestResultRepository : BaseRepository<TestExecutionResult>, ITestResultRepository
{
    /// <summary>
    /// Initializes a new instance of the TestResultRepository class
    /// </summary>
    /// <param name="context">Database context</param>
    /// <param name="logger">Logger instance</param>
    public TestResultRepository(ArtDesignFrameworkDbContext context, ILogger<TestResultRepository> logger)
        : base(context, logger)
    {
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetByTestSuiteAsync(string testSuite, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting test results by test suite: {TestSuite}", testSuite);
            return await DbSet.Where(t => t.TestSuite == testSuite)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get test results by test suite: {TestSuite}", testSuite);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetByTestNameAsync(string testName, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting test results by test name: {TestName}", testName);
            return await DbSet.Where(t => t.TestName == testName)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get test results by test name: {TestName}", testName);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetBySessionAsync(Guid sessionId, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting test results by session: {SessionId}", sessionId);
            return await DbSet.Where(t => t.SessionId == sessionId)
                .OrderBy(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get test results by session: {SessionId}", sessionId);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetByStatusAsync(bool passed, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting test results by status: {Passed}", passed);
            return await DbSet.Where(t => t.Passed == passed)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get test results by status: {Passed}", passed);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting test results by date range: {StartDate} to {EndDate}", startDate, endDate);
            return await DbSet.Where(t => t.CreatedAt >= startDate && t.CreatedAt <= endDate)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get test results by date range: {StartDate} to {EndDate}", startDate, endDate);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting test results by environment: {Environment}", environment);
            return await DbSet.Where(t => t.Environment == environment)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get test results by environment: {Environment}", environment);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting test results by category: {Category}", category);
            return await DbSet.Where(t => t.TestCategory == category)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get test results by category: {Category}", category);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetFailedTestsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting failed test results");
            return await DbSet.Where(t => !t.Passed && !string.IsNullOrEmpty(t.ErrorMessage))
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get failed test results");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetSlowTestsAsync(double thresholdMs, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting slow test results with threshold: {ThresholdMs}ms", thresholdMs);
            return await DbSet.Where(t => t.ExecutionTimeMs > thresholdMs)
                .OrderByDescending(t => t.ExecutionTimeMs)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get slow test results with threshold: {ThresholdMs}ms", thresholdMs);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<TestExecutionStatistics> GetTestSuiteStatisticsAsync(string testSuite, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting test suite statistics for: {TestSuite}", testSuite);

            var tests = await DbSet.Where(t => t.TestSuite == testSuite).ToListAsync(cancellationToken);

            if (!tests.Any())
            {
                return new TestExecutionStatistics();
            }

            return new TestExecutionStatistics
            {
                TotalTests = tests.Count,
                PassedTests = tests.Count(t => t.Passed),
                FailedTests = tests.Count(t => !t.Passed),
                AverageExecutionTimeMs = tests.Average(t => t.ExecutionTimeMs),
                TotalExecutionTimeMs = tests.Sum(t => t.ExecutionTimeMs),
                MinExecutionTimeMs = tests.Min(t => t.ExecutionTimeMs),
                MaxExecutionTimeMs = tests.Max(t => t.ExecutionTimeMs),
                AverageMemoryUsageBytes = (long)tests.Average(t => t.MemoryUsageBytes),
                PeakMemoryUsageBytes = tests.Max(t => t.MemoryUsageBytes),
                AverageCpuUsagePercent = tests.Average(t => t.CpuUsagePercent)
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get test suite statistics for: {TestSuite}", testSuite);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<TestExecutionStatistics> GetStatisticsByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting test statistics by date range: {StartDate} to {EndDate}", startDate, endDate);

            var tests = await DbSet.Where(t => t.CreatedAt >= startDate && t.CreatedAt <= endDate)
                .ToListAsync(cancellationToken);

            if (!tests.Any())
            {
                return new TestExecutionStatistics();
            }

            return new TestExecutionStatistics
            {
                TotalTests = tests.Count,
                PassedTests = tests.Count(t => t.Passed),
                FailedTests = tests.Count(t => !t.Passed),
                AverageExecutionTimeMs = tests.Average(t => t.ExecutionTimeMs),
                TotalExecutionTimeMs = tests.Sum(t => t.ExecutionTimeMs),
                MinExecutionTimeMs = tests.Min(t => t.ExecutionTimeMs),
                MaxExecutionTimeMs = tests.Max(t => t.ExecutionTimeMs),
                AverageMemoryUsageBytes = (long)tests.Average(t => t.MemoryUsageBytes),
                PeakMemoryUsageBytes = tests.Max(t => t.MemoryUsageBytes),
                AverageCpuUsagePercent = tests.Average(t => t.CpuUsagePercent)
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get test statistics by date range: {StartDate} to {EndDate}", startDate, endDate);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetRecentTestResultsAsync(string testSuite, string testName, int count = 10, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting recent test results for {TestSuite}.{TestName}, count: {Count}", testSuite, testName, count);
            return await DbSet.Where(t => t.TestSuite == testSuite && t.TestName == testName)
                .OrderByDescending(t => t.CreatedAt)
                .Take(count)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get recent test results for {TestSuite}.{TestName}", testSuite, testName);
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetRegressionTestsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting regression test results");
            return await DbSet.Where(t => t.IsRegressionTest)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get regression test results");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<IEnumerable<TestExecutionResult>> GetAutomatedBuildTestsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting automated build test results");
            return await DbSet.Where(t => t.IsAutomatedBuild)
                .OrderByDescending(t => t.CreatedAt)
                .ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get automated build test results");
            throw;
        }
    }

    /// <inheritdoc />
    public async Task<int> CleanupOldResultsAsync(int retentionDays, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogInformation("Cleaning up test results older than {RetentionDays} days", retentionDays);

            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
            var oldResults = await DbSet.Where(t => t.CreatedAt < cutoffDate).ToListAsync(cancellationToken);

            if (oldResults.Any())
            {
                DbSet.RemoveRange(oldResults);
                await Context.SaveChangesAsync(cancellationToken);

                Logger.LogInformation("Cleaned up {Count} old test results", oldResults.Count);
            }

            return oldResults.Count;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to cleanup old test results");
            throw;
        }
    }
}
