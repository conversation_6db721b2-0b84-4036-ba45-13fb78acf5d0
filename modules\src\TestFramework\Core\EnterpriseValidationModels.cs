using System;
using System.Collections.Generic;

namespace ArtDesignFramework.TestFramework.Core
{
    /// <summary>
    /// Enterprise validation result containing comprehensive framework assessment
    /// </summary>
    public class EnterpriseValidationResult
    {
        /// <summary>
        /// Gets or sets the validation ID
        /// </summary>
        public string ValidationId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the validation start time
        /// </summary>
        public DateTime ValidationStartTime { get; set; }

        /// <summary>
        /// Gets or sets the validation end time
        /// </summary>
        public DateTime ValidationEndTime { get; set; }

        /// <summary>
        /// Gets or sets the validation duration
        /// </summary>
        public TimeSpan ValidationDuration { get; set; }

        /// <summary>
        /// Gets or sets the total number of modules discovered
        /// </summary>
        public int TotalModulesDiscovered { get; set; }

        /// <summary>
        /// Gets or sets the number of healthy modules
        /// </summary>
        public int HealthyModules { get; set; }

        /// <summary>
        /// Gets or sets the number of buildable modules
        /// </summary>
        public int BuildableModules { get; set; }

        /// <summary>
        /// Gets or sets the module health validation results
        /// </summary>
        public List<ModuleHealthValidationResult> ModuleHealthResults { get; set; } = new List<ModuleHealthValidationResult>();

        /// <summary>
        /// Gets or sets the build validation results
        /// </summary>
        public List<ComprehensiveBuildValidationResult> BuildValidationResults { get; set; } = new List<ComprehensiveBuildValidationResult>();

        /// <summary>
        /// Gets or sets the status mismatches found
        /// </summary>
        public List<StatusMismatch> StatusMismatches { get; set; } = new List<StatusMismatch>();

        /// <summary>
        /// Gets or sets whether there are status mismatches
        /// </summary>
        public bool HasStatusMismatches { get; set; }

        /// <summary>
        /// Gets or sets the phantom modules detected
        /// </summary>
        public List<PhantomModule> PhantomModules { get; set; } = new List<PhantomModule>();

        /// <summary>
        /// Gets or sets whether there are phantom modules
        /// </summary>
        public bool HasPhantomModules { get; set; }

        /// <summary>
        /// Gets or sets the quality gate results
        /// </summary>
        public List<QualityGateResult> QualityGates { get; set; } = new List<QualityGateResult>();

        /// <summary>
        /// Gets or sets the number of quality gates passed
        /// </summary>
        public int QualityGatesPassed { get; set; }

        /// <summary>
        /// Gets or sets the overall quality score (0-100)
        /// </summary>
        public double OverallQualityScore { get; set; }

        /// <summary>
        /// Gets or sets the critical errors encountered
        /// </summary>
        public List<string> CriticalErrors { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets whether the overall validation was successful
        /// </summary>
        public bool OverallSuccess { get; set; }

        /// <summary>
        /// Gets or sets the comprehensive testing infrastructure results
        /// Last Updated: 2025-01-09 23:55:00 UTC
        /// </summary>
        public ComprehensiveTestSuiteResult? ComprehensiveTestResults { get; set; }
    }

    /// <summary>
    /// Represents a discovered module in the framework
    /// </summary>
    public class DiscoveredModule
    {
        /// <summary>
        /// Gets or sets the module name
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the module path
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the module has a project file
        /// </summary>
        public bool HasProjectFile { get; set; }

        /// <summary>
        /// Gets or sets the project file path
        /// </summary>
        public string? ProjectFilePath { get; set; }

        /// <summary>
        /// Gets or sets the number of source files
        /// </summary>
        public int SourceFileCount { get; set; }
    }

    /// <summary>
    /// Represents a status mismatch between claimed and actual status
    /// </summary>
    public class StatusMismatch
    {
        /// <summary>
        /// Gets or sets the module name
        /// </summary>
        public string ModuleName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the claimed status
        /// </summary>
        public string ClaimedStatus { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the actual status
        /// </summary>
        public string ActualStatus { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the list of issues causing the mismatch
        /// </summary>
        public List<string> Issues { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the severity of the mismatch
        /// </summary>
        public MismatchSeverity Severity { get; set; } = MismatchSeverity.High;
    }

    /// <summary>
    /// Represents a phantom module (claims functionality it doesn't have)
    /// </summary>
    public class PhantomModule
    {
        /// <summary>
        /// Gets or sets the module name
        /// </summary>
        public string ModuleName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the claimed status
        /// </summary>
        public string ClaimedStatus { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the reasons why it's considered phantom
        /// </summary>
        public List<string> PhantomReasons { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the detection timestamp
        /// </summary>
        public DateTime DetectedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Severity levels for status mismatches
    /// </summary>
    public enum MismatchSeverity
    {
        /// <summary>
        /// Low severity mismatch
        /// </summary>
        Low,

        /// <summary>
        /// Medium severity mismatch
        /// </summary>
        Medium,

        /// <summary>
        /// High severity mismatch
        /// </summary>
        High,

        /// <summary>
        /// Critical severity mismatch
        /// </summary>
        Critical
    }

    /// <summary>
    /// Continuous monitoring configuration
    /// </summary>
    public class ContinuousMonitoringConfig
    {
        /// <summary>
        /// Gets or sets whether continuous monitoring is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Gets or sets the monitoring interval in minutes
        /// </summary>
        public int MonitoringIntervalMinutes { get; set; } = 60;

        /// <summary>
        /// Gets or sets the paths to monitor
        /// </summary>
        public List<string> MonitoredPaths { get; set; } = new List<string>
        {
            "modules/production/src"
        };

        /// <summary>
        /// Gets or sets the alert configuration
        /// </summary>
        public MonitoringAlertConfig AlertConfig { get; set; } = new MonitoringAlertConfig();

        /// <summary>
        /// Gets or sets whether to auto-fix certain issues
        /// </summary>
        public bool AutoFixEnabled { get; set; } = false;

        /// <summary>
        /// Gets or sets the types of issues to auto-fix
        /// </summary>
        public List<AutoFixType> AutoFixTypes { get; set; } = new List<AutoFixType>();
    }

    /// <summary>
    /// Types of issues that can be auto-fixed
    /// </summary>
    public enum AutoFixType
    {
        /// <summary>
        /// Fix missing project files
        /// </summary>
        MissingProjectFiles,

        /// <summary>
        /// Fix documentation issues
        /// </summary>
        DocumentationIssues,

        /// <summary>
        /// Fix code style violations
        /// </summary>
        CodeStyleViolations,

        /// <summary>
        /// Update status claims
        /// </summary>
        StatusClaims
    }

    /// <summary>
    /// Performance regression detection configuration
    /// </summary>
    public class PerformanceRegressionConfig
    {
        /// <summary>
        /// Gets or sets whether performance regression detection is enabled
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// Gets or sets the baseline performance data path
        /// </summary>
        public string BaselineDataPath { get; set; } = "TestResults/PerformanceBaselines";

        /// <summary>
        /// Gets or sets the regression threshold percentage
        /// </summary>
        public double RegressionThresholdPercent { get; set; } = 10.0;

        /// <summary>
        /// Gets or sets the metrics to monitor
        /// </summary>
        public List<PerformanceMetric> MonitoredMetrics { get; set; } = new List<PerformanceMetric>
        {
            PerformanceMetric.ExecutionTime,
            PerformanceMetric.MemoryUsage,
            PerformanceMetric.BuildTime
        };
    }

    /// <summary>
    /// Performance metrics to monitor
    /// </summary>
    public enum PerformanceMetric
    {
        /// <summary>
        /// Execution time metric
        /// </summary>
        ExecutionTime,

        /// <summary>
        /// Memory usage metric
        /// </summary>
        MemoryUsage,

        /// <summary>
        /// Build time metric
        /// </summary>
        BuildTime,

        /// <summary>
        /// Test execution time metric
        /// </summary>
        TestExecutionTime,

        /// <summary>
        /// Startup time metric
        /// </summary>
        StartupTime
    }

    /// <summary>
    /// Test coverage enhancement configuration
    /// </summary>
    public class TestCoverageEnhancementConfig
    {
        /// <summary>
        /// Gets or sets the minimum coverage threshold
        /// </summary>
        public double MinimumCoverageThreshold { get; set; } = 0.8;

        /// <summary>
        /// Gets or sets whether to auto-generate missing tests
        /// </summary>
        public bool AutoGenerateMissingTests { get; set; } = false;

        /// <summary>
        /// Gets or sets the test generation templates to use
        /// </summary>
        public List<string> TestGenerationTemplates { get; set; } = new List<string>
        {
            "UnitTest", "IntegrationTest", "PerformanceTest"
        };

        /// <summary>
        /// Gets or sets whether to validate test quality
        /// </summary>
        public bool ValidateTestQuality { get; set; } = true;
    }
}
