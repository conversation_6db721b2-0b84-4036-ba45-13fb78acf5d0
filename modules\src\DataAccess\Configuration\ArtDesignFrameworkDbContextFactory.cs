// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.DataAccess.Configuration;

/// <summary>
/// Design-time factory for creating ArtDesignFrameworkDbContext instances
/// Used by Entity Framework tools for migrations and scaffolding
/// </summary>
public class ArtDesignFrameworkDbContextFactory : IDesignTimeDbContextFactory<ArtDesignFrameworkDbContext>
{
    /// <summary>
    /// Creates a new instance of ArtDesignFrameworkDbContext for design-time operations
    /// </summary>
    /// <param name="args">Command line arguments</param>
    /// <returns>Configured DbContext instance</returns>
    public ArtDesignFrameworkDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<ArtDesignFrameworkDbContext>();

        // Configure SQLite connection for design-time
        var dataDirectory = Path.Combine("L:", "framework", "data");
        var databasePath = Path.Combine(dataDirectory, "ArtDesignFramework.db");
        var connectionString = $"Data Source={databasePath}";

        optionsBuilder.UseSqlite(connectionString, options =>
        {
            options.CommandTimeout(30);
        });

        // Enable detailed errors for design-time
        optionsBuilder.EnableDetailedErrors();
        optionsBuilder.EnableSensitiveDataLogging();

        // Create simple logger factory for design-time
        var loggerFactory = LoggerFactory.Create(builder =>
            builder.SetMinimumLevel(LogLevel.Information));

        optionsBuilder.UseLoggerFactory(loggerFactory);

        return new ArtDesignFrameworkDbContext(optionsBuilder.Options);
    }
}
