// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using Microsoft.Data.Sqlite;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ArtDesignFramework.DataAccess.Configuration;

/// <summary>
/// Database initialization and setup logic for ArtDesignFramework
/// </summary>
public class DatabaseInitializer
{
    private readonly ArtDesignFrameworkDbContext _context;
    private readonly DataAccessOptions _options;
    private readonly ILogger<DatabaseInitializer> _logger;

    /// <summary>
    /// Initializes a new instance of the DatabaseInitializer class
    /// </summary>
    /// <param name="context">Database context</param>
    /// <param name="options">DataAccess options</param>
    /// <param name="logger">Logger instance</param>
    public DatabaseInitializer(
        ArtDesignFrameworkDbContext context,
        IOptions<DataAccessOptions> options,
        ILogger<DatabaseInitializer> logger)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Initializes the database with proper configuration
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting database initialization");

            // Ensure data directory exists
            await EnsureDataDirectoryExistsAsync();

            // Configure SQLite connection
            await ConfigureSqliteConnectionAsync();

            // Create database if it doesn't exist
            var created = await _context.EnsureDatabaseCreatedAsync();

            if (created)
            {
                _logger.LogInformation("Database created successfully");
            }

            // Apply any pending migrations
            if (_options.EnableAutomaticMigrations)
            {
                await ApplyMigrationsAsync(cancellationToken);
            }

            // Verify database integrity
            await VerifyDatabaseIntegrityAsync();

            _logger.LogInformation("Database initialization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize database");
            throw;
        }
    }

    /// <summary>
    /// Ensures the data directory exists
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    private async Task EnsureDataDirectoryExistsAsync()
    {
        var dataDirectory = Path.Combine("L:", "framework", "data");

        if (!Directory.Exists(dataDirectory))
        {
            _logger.LogInformation("Creating data directory: {DataDirectory}", dataDirectory);
            Directory.CreateDirectory(dataDirectory);

            // Set appropriate permissions if needed
            await Task.CompletedTask; // Placeholder for permission setting
        }
        else
        {
            _logger.LogDebug("Data directory already exists: {DataDirectory}", dataDirectory);
        }
    }

    /// <summary>
    /// Configures SQLite connection with optimal settings
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    private async Task ConfigureSqliteConnectionAsync()
    {
        try
        {
            _logger.LogDebug("Configuring SQLite connection settings");

            // Open a connection to configure SQLite settings
            await using var connection = _context.Database.GetDbConnection() as SqliteConnection;
            if (connection != null)
            {
                await connection.OpenAsync();

                // Enable WAL mode for better concurrent access
                await using var walCommand = connection.CreateCommand();
                walCommand.CommandText = "PRAGMA journal_mode=WAL;";
                var walResult = await walCommand.ExecuteScalarAsync();
                _logger.LogDebug("WAL mode set: {Result}", walResult);

                // Enable foreign key constraints
                await using var fkCommand = connection.CreateCommand();
                fkCommand.CommandText = "PRAGMA foreign_keys=ON;";
                await fkCommand.ExecuteNonQueryAsync();
                _logger.LogDebug("Foreign key constraints enabled");

                // Set synchronous mode for better performance
                await using var syncCommand = connection.CreateCommand();
                syncCommand.CommandText = "PRAGMA synchronous=NORMAL;";
                await syncCommand.ExecuteNonQueryAsync();
                _logger.LogDebug("Synchronous mode set to NORMAL");

                // Set cache size for better performance
                await using var cacheCommand = connection.CreateCommand();
                cacheCommand.CommandText = "PRAGMA cache_size=10000;";
                await cacheCommand.ExecuteNonQueryAsync();
                _logger.LogDebug("Cache size set to 10000 pages");

                // Set temp store to memory for better performance
                await using var tempCommand = connection.CreateCommand();
                tempCommand.CommandText = "PRAGMA temp_store=MEMORY;";
                await tempCommand.ExecuteNonQueryAsync();
                _logger.LogDebug("Temp store set to memory");

                await connection.CloseAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to configure SQLite connection settings");
            // Don't throw - these are optimizations, not requirements
        }
    }

    /// <summary>
    /// Applies any pending migrations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    private async Task ApplyMigrationsAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Checking for pending migrations");

            var pendingMigrations = await _context.Database.GetPendingMigrationsAsync(cancellationToken);
            var pendingList = pendingMigrations.ToList();

            if (pendingList.Count > 0)
            {
                _logger.LogInformation("Applying {Count} pending migrations: {Migrations}",
                    pendingList.Count, string.Join(", ", pendingList));

                await _context.Database.MigrateAsync(cancellationToken);

                _logger.LogInformation("Migrations applied successfully");
            }
            else
            {
                _logger.LogDebug("No pending migrations found");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply migrations");
            throw;
        }
    }

    /// <summary>
    /// Verifies database integrity and structure
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    private async Task VerifyDatabaseIntegrityAsync()
    {
        try
        {
            _logger.LogDebug("Verifying database integrity");

            // Check if all expected tables exist
            var tableNames = new[]
            {
                "TestExecutionResults",
                "PerformanceBenchmarks",
                "ModuleHealthStatuses",
                "FrameworkConfigurations",
                "UserPreferences"
            };

            await using var connection = _context.Database.GetDbConnection() as SqliteConnection;
            if (connection != null)
            {
                await connection.OpenAsync();

                foreach (var tableName in tableNames)
                {
                    await using var command = connection.CreateCommand();
                    command.CommandText = "SELECT name FROM sqlite_master WHERE type='table' AND name=@tableName;";
                    command.Parameters.AddWithValue("@tableName", tableName);
                    var result = await command.ExecuteScalarAsync();

                    if (result == null)
                    {
                        throw new InvalidOperationException($"Expected table '{tableName}' not found in database");
                    }
                }

                // Run integrity check
                await using var integrityCommand = connection.CreateCommand();
                integrityCommand.CommandText = "PRAGMA integrity_check;";
                var integrityResult = await integrityCommand.ExecuteScalarAsync();

                if (integrityResult?.ToString() != "ok")
                {
                    _logger.LogWarning("Database integrity check returned: {Result}", integrityResult);
                }
                else
                {
                    _logger.LogDebug("Database integrity check passed");
                }

                await connection.CloseAsync();
            }

            _logger.LogInformation("Database verification completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database verification failed");
            throw;
        }
    }

    /// <summary>
    /// Performs database cleanup based on retention policies
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    public async Task CleanupAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting database cleanup");

            var retentionDays = _options.DataRetentionDays;
            var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);

            // Clean up old test execution results
            var oldTestResults = await _context.TestExecutionResults
                .Where(t => t.CreatedAt < cutoffDate)
                .CountAsync(cancellationToken);

            if (oldTestResults > 0)
            {
                _context.TestExecutionResults.RemoveRange(
                    _context.TestExecutionResults.Where(t => t.CreatedAt < cutoffDate));

                _logger.LogInformation("Removed {Count} old test execution results", oldTestResults);
            }

            // Clean up old performance benchmarks
            var oldBenchmarks = await _context.PerformanceBenchmarks
                .Where(p => p.CreatedAt < cutoffDate)
                .CountAsync(cancellationToken);

            if (oldBenchmarks > 0)
            {
                _context.PerformanceBenchmarks.RemoveRange(
                    _context.PerformanceBenchmarks.Where(p => p.CreatedAt < cutoffDate));

                _logger.LogInformation("Removed {Count} old performance benchmarks", oldBenchmarks);
            }

            // Clean up old module health statuses (keep latest for each module)
            var oldHealthStatuses = await _context.ModuleHealthStatuses
                .Where(h => h.CreatedAt < cutoffDate)
                .GroupBy(h => h.ModuleName)
                .SelectMany(g => g.OrderByDescending(h => h.CreatedAt).Skip(5)) // Keep latest 5 per module
                .CountAsync(cancellationToken);

            if (oldHealthStatuses > 0)
            {
                var statusesToRemove = _context.ModuleHealthStatuses
                    .Where(h => h.CreatedAt < cutoffDate)
                    .GroupBy(h => h.ModuleName)
                    .SelectMany(g => g.OrderByDescending(h => h.CreatedAt).Skip(5));

                _context.ModuleHealthStatuses.RemoveRange(statusesToRemove);

                _logger.LogInformation("Removed {Count} old module health statuses", oldHealthStatuses);
            }

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Database cleanup completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Database cleanup failed");
            throw;
        }
    }
}
