// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.Linq.Expressions;
using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Base repository implementation providing common data access operations
/// </summary>
/// <typeparam name="TEntity">Entity type</typeparam>
public abstract class BaseRepository<TEntity> : IBaseRepository<TEntity> where TEntity : class
{
    protected readonly ArtDesignFrameworkDbContext Context;
    protected readonly DbSet<TEntity> DbSet;
    protected readonly ILogger Logger;

    /// <summary>
    /// Initializes a new instance of the BaseRepository class
    /// </summary>
    /// <param name="context">Database context</param>
    /// <param name="logger">Logger instance</param>
    protected BaseRepository(ArtDesignFrameworkDbContext context, ILogger logger)
    {
        Context = context ?? throw new ArgumentNullException(nameof(context));
        DbSet = context.Set<TEntity>();
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public virtual async Task<TEntity?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting entity by ID: {Id}", id);
            return await DbSet.FindAsync(new object[] { id }, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get entity by ID: {Id}", id);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<IEnumerable<TEntity>> GetAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting all entities of type {EntityType}", typeof(TEntity).Name);
            return await DbSet.ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get all entities of type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<IEnumerable<TEntity>> GetWhereAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting entities with predicate for type {EntityType}", typeof(TEntity).Name);
            return await DbSet.Where(predicate).ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get entities with predicate for type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<TEntity?> GetSingleAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting single entity with predicate for type {EntityType}", typeof(TEntity).Name);
            return await DbSet.FirstOrDefaultAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get single entity with predicate for type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<PagedResult<TEntity>> GetPagedAsync(int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting paged entities for type {EntityType}, page {PageNumber}, size {PageSize}",
                typeof(TEntity).Name, pageNumber, pageSize);

            var totalCount = await DbSet.CountAsync(cancellationToken);
            var items = await DbSet
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<TEntity>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get paged entities for type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<PagedResult<TEntity>> GetPagedAsync(Expression<Func<TEntity, bool>> predicate, int pageNumber, int pageSize, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting paged entities with predicate for type {EntityType}, page {PageNumber}, size {PageSize}",
                typeof(TEntity).Name, pageNumber, pageSize);

            var query = DbSet.Where(predicate);
            var totalCount = await query.CountAsync(cancellationToken);
            var items = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);

            return new PagedResult<TEntity>
            {
                Items = items,
                TotalCount = totalCount,
                PageNumber = pageNumber,
                PageSize = pageSize
            };
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get paged entities with predicate for type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<IEnumerable<TEntity>> GetOrderedAsync<TKey>(Expression<Func<TEntity, TKey>> orderBy, bool ascending = true, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Getting ordered entities for type {EntityType}, ascending: {Ascending}",
                typeof(TEntity).Name, ascending);

            var query = ascending ? DbSet.OrderBy(orderBy) : DbSet.OrderByDescending(orderBy);
            return await query.ToListAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to get ordered entities for type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<bool> AnyAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Checking if any entity exists with predicate for type {EntityType}", typeof(TEntity).Name);
            return await DbSet.AnyAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to check if any entity exists for type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<int> CountAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Counting entities with predicate for type {EntityType}", typeof(TEntity).Name);
            return await DbSet.CountAsync(predicate, cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to count entities for type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<TEntity> AddAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Adding entity of type {EntityType}", typeof(TEntity).Name);
            var entry = await DbSet.AddAsync(entity, cancellationToken);
            await Context.SaveChangesAsync(cancellationToken);
            return entry.Entity;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to add entity of type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task AddRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
    {
        try
        {
            var entityList = entities.ToList();
            Logger.LogDebug("Adding {Count} entities of type {EntityType}", entityList.Count, typeof(TEntity).Name);
            await DbSet.AddRangeAsync(entityList, cancellationToken);
            await Context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to add entities of type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<TEntity> UpdateAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Updating entity of type {EntityType}", typeof(TEntity).Name);
            DbSet.Update(entity);
            await Context.SaveChangesAsync(cancellationToken);
            return entity;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to update entity of type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task UpdateRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
    {
        try
        {
            var entityList = entities.ToList();
            Logger.LogDebug("Updating {Count} entities of type {EntityType}", entityList.Count, typeof(TEntity).Name);
            DbSet.UpdateRange(entityList);
            await Context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to update entities of type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<bool> DeleteAsync(Guid id, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Deleting entity by ID: {Id}", id);
            var entity = await GetByIdAsync(id, cancellationToken);
            if (entity == null)
            {
                return false;
            }

            DbSet.Remove(entity);
            await Context.SaveChangesAsync(cancellationToken);
            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to delete entity by ID: {Id}", id);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task DeleteAsync(TEntity entity, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Deleting entity of type {EntityType}", typeof(TEntity).Name);
            DbSet.Remove(entity);
            await Context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to delete entity of type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task DeleteRangeAsync(IEnumerable<TEntity> entities, CancellationToken cancellationToken = default)
    {
        try
        {
            var entityList = entities.ToList();
            Logger.LogDebug("Deleting {Count} entities of type {EntityType}", entityList.Count, typeof(TEntity).Name);
            DbSet.RemoveRange(entityList);
            await Context.SaveChangesAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to delete entities of type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }

    /// <inheritdoc />
    public virtual async Task<int> DeleteWhereAsync(Expression<Func<TEntity, bool>> predicate, CancellationToken cancellationToken = default)
    {
        try
        {
            Logger.LogDebug("Deleting entities with predicate for type {EntityType}", typeof(TEntity).Name);
            var entities = await DbSet.Where(predicate).ToListAsync(cancellationToken);
            DbSet.RemoveRange(entities);
            await Context.SaveChangesAsync(cancellationToken);
            return entities.Count;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Failed to delete entities with predicate for type {EntityType}", typeof(TEntity).Name);
            throw;
        }
    }
}
