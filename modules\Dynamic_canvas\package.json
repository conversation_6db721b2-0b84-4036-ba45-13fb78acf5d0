{"name": "streamlined-digital-art-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:comprehensive": "vitest run src/__tests__/comprehensive-functional.test.ts", "test:automated": "tsx src/__tests__/automated-test-runner.ts", "test:coverage": "vitest run --coverage", "test:watch": "vitest", "test:ui": "vitest --ui", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "dependencies": {"eventemitter3": "^5.0.1", "fabric": "^5.3.0", "file-saver": "^2.0.5", "jszip": "^3.10.1", "konva": "^9.2.0", "react": "^18.2.0", "react-color": "^2.19.3", "react-dom": "^18.2.0", "react-konva": "^18.2.10", "uuid": "^9.0.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/file-saver": "^2.0.7", "@types/react": "^18.2.43", "@types/react-color": "^3.0.9", "@types/react-dom": "^18.2.17", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "canvas": "^3.0.0", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^26.1.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "tsx": "^4.6.2", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}, "main": "public/electron-main.js", "homepage": "./", "build": {"appId": "com.digitalartframework.app", "productName": "Digital Art Framework", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "public/electron-main.js", "node_modules/**/*"], "win": {"target": "nsis", "icon": "public/favicon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}