// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Configuration;
using ArtDesignFramework.DataAccess.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository implementation for framework configuration data access
/// </summary>
public class ConfigurationRepository : BaseRepository<FrameworkConfiguration>, IConfigurationRepository
{
    public ConfigurationRepository(ArtDesignFrameworkDbContext context, ILogger<ConfigurationRepository> logger)
        : base(context, logger) { }

    public async Task<FrameworkConfiguration?> GetByKeyAsync(string key, CancellationToken cancellationToken = default)
        => await DbSet.FirstOrDefaultAsync(c => c.ConfigurationKey == key, cancellationToken);

    public async Task<string?> GetValueAsync(string key, CancellationToken cancellationToken = default)
    {
        var config = await GetByKeyAsync(key, cancellationToken);
        return config?.ConfigurationValue;
    }

    public async Task<string> GetValueOrDefaultAsync(string key, string defaultValue, CancellationToken cancellationToken = default)
    {
        var value = await GetValueAsync(key, cancellationToken);
        return value ?? defaultValue;
    }

    public async Task<IEnumerable<FrameworkConfiguration>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.Category == category).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetByScopeAsync(string scope, CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.Scope == scope).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.Environment == environment || c.Environment == "All").OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetByModuleAsync(string moduleName, CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.ModuleName == moduleName).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetByUserAsync(string userId, CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.UserId == userId).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetFeatureFlagsAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.IsFeatureFlag).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetActiveConfigurationsAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.IsActive).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetEnvironmentSpecificConfigurationsAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.IsEnvironmentSpecific).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetUserSpecificConfigurationsAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.IsUserSpecific).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetConfigurationsRequiringRestartAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.RequiresRestart).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetReadOnlyConfigurationsAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.IsReadOnly).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<FrameworkConfiguration>> GetEncryptedConfigurationsAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(c => c.IsEncrypted).OrderBy(c => c.ConfigurationKey).ToListAsync(cancellationToken);

    public async Task<FrameworkConfiguration> SetValueAsync(string key, string value, CancellationToken cancellationToken = default)
    {
        var config = await GetByKeyAsync(key, cancellationToken);
        if (config != null)
        {
            config.ConfigurationValue = value;
            config.UpdatedAt = DateTime.UtcNow;
            config.AccessCount++;
            config.LastAccessedAt = DateTime.UtcNow;
            return await UpdateAsync(config, cancellationToken);
        }

        var newConfig = new FrameworkConfiguration
        {
            ConfigurationKey = key,
            ConfigurationValue = value,
            DataType = "String",
            Category = "General",
            Scope = "Global",
            Environment = "All"
        };
        return await AddAsync(newConfig, cancellationToken);
    }

    public async Task SetValuesAsync(Dictionary<string, string> configurations, CancellationToken cancellationToken = default)
    {
        foreach (var kvp in configurations)
        {
            await SetValueAsync(kvp.Key, kvp.Value, cancellationToken);
        }
    }

    public async Task<ConfigurationValidationResult> ValidateValueAsync(string key, string value, CancellationToken cancellationToken = default)
    {
        var config = await GetByKeyAsync(key, cancellationToken);
        if (config == null)
        {
            return new ConfigurationValidationResult
            {
                IsValid = false,
                ErrorMessage = "Configuration key not found"
            };
        }

        // Basic validation logic - can be extended
        var result = new ConfigurationValidationResult { IsValid = true, ValidatedValue = value };

        if (!string.IsNullOrEmpty(config.ValidationPattern))
        {
            var regex = new System.Text.RegularExpressions.Regex(config.ValidationPattern);
            if (!regex.IsMatch(value))
            {
                result.IsValid = false;
                result.ErrorMessage = config.ValidationErrorMessage ?? "Value does not match required pattern";
            }
        }

        return result;
    }

    public async Task<ConfigurationStatistics> GetAccessStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var configs = await DbSet.ToListAsync(cancellationToken);
        var mostAccessed = configs.OrderByDescending(c => c.AccessCount).FirstOrDefault();

        return new ConfigurationStatistics
        {
            TotalConfigurations = configs.Count,
            ActiveConfigurations = configs.Count(c => c.IsActive),
            FeatureFlags = configs.Count(c => c.IsFeatureFlag),
            EnvironmentSpecificConfigurations = configs.Count(c => c.IsEnvironmentSpecific),
            UserSpecificConfigurations = configs.Count(c => c.IsUserSpecific),
            TotalAccessCount = configs.Sum(c => c.AccessCount),
            AverageAccessCount = configs.Any() ? configs.Average(c => c.AccessCount) : 0,
            MostAccessedKey = mostAccessed?.ConfigurationKey,
            MostAccessedCount = mostAccessed?.AccessCount ?? 0
        };
    }

    public async Task<IEnumerable<FrameworkConfiguration>> GetMostAccessedConfigurationsAsync(int count = 10, CancellationToken cancellationToken = default)
        => await DbSet.OrderByDescending(c => c.AccessCount).Take(count).ToListAsync(cancellationToken);

    public async Task UpdateAccessCountAsync(string key, CancellationToken cancellationToken = default)
    {
        var config = await GetByKeyAsync(key, cancellationToken);
        if (config != null)
        {
            config.AccessCount++;
            config.LastAccessedAt = DateTime.UtcNow;
            await UpdateAsync(config, cancellationToken);
        }
    }

    public async Task<Dictionary<string, string>> ExportConfigurationsAsync(string? scope = null, string? environment = null, CancellationToken cancellationToken = default)
    {
        var query = DbSet.AsQueryable();

        if (!string.IsNullOrEmpty(scope))
            query = query.Where(c => c.Scope == scope);

        if (!string.IsNullOrEmpty(environment))
            query = query.Where(c => c.Environment == environment || c.Environment == "All");

        var configs = await query.ToListAsync(cancellationToken);
        return configs.ToDictionary(c => c.ConfigurationKey, c => c.ConfigurationValue ?? string.Empty);
    }

    public async Task<int> ImportConfigurationsAsync(Dictionary<string, string> configurations, bool overwriteExisting = false, CancellationToken cancellationToken = default)
    {
        int imported = 0;
        foreach (var kvp in configurations)
        {
            var existing = await GetByKeyAsync(kvp.Key, cancellationToken);
            if (existing == null || overwriteExisting)
            {
                await SetValueAsync(kvp.Key, kvp.Value, cancellationToken);
                imported++;
            }
        }
        return imported;
    }
}
