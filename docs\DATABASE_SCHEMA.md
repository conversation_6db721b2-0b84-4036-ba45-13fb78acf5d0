# ArtDesignFramework Database Schema Documentation

**Last Updated:** 2025-06-08 21:30:00 UTC

## Overview

This document provides comprehensive documentation for the ArtDesignFramework database schema, implemented using Entity Framework Core with SQLite. The schema is designed to support test execution tracking, performance benchmarking, module health monitoring, framework configuration management, and user preference storage.

## Database Configuration

### Connection Details
- **Database Type**: SQLite
- **Location**: `L:\framework\data\ArtDesignFramework.db`
- **Entity Framework Version**: 9.0.0
- **Schema Version**: 1.0

### Entity Framework Context
```csharp
public class ArtDesignFrameworkDbContext : DbContext
{
    public DbSet<TestExecutionResult> TestExecutionResults { get; set; }
    public DbSet<PerformanceBenchmark> PerformanceBenchmarks { get; set; }
    public DbSet<ModuleHealthStatus> ModuleHealthStatuses { get; set; }
    public DbSet<FrameworkConfiguration> FrameworkConfigurations { get; set; }
    public DbSet<UserPreference> UserPreferences { get; set; }
}
```

## Table Schemas

### TestExecutionResults
Stores comprehensive test execution data and validation results.

#### Columns
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | GUID | PRIMARY KEY | Unique identifier |
| TestName | NVARCHAR(200) | NOT NULL, INDEX | Name of the executed test |
| TestType | INT | NOT NULL | Type of test (Unit, Integration, Performance) |
| Status | INT | NOT NULL | Test execution status (Passed, Failed, Skipped) |
| ExecutionTimeMs | REAL | NOT NULL | Test execution time in milliseconds |
| ResultData | NTEXT | NULL | Detailed test result data |
| ErrorMessage | NTEXT | NULL | Error message if test failed |
| StackTrace | NTEXT | NULL | Stack trace for failed tests |
| SessionId | GUID | NULL, INDEX | Session identifier for grouping related tests |
| Environment | NVARCHAR(50) | NULL | Execution environment (Development, Testing, Production) |
| MachineName | NVARCHAR(100) | NULL | Machine where test was executed |
| FrameworkVersion | NVARCHAR(20) | NULL | Framework version during test execution |
| BuildVersion | NVARCHAR(50) | NULL | Build version information |
| Tags | NVARCHAR(500) | NULL | Comma-separated tags for categorization |
| CreatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| UpdatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Last update timestamp |

#### Indexes
- `IX_TestExecutionResults_TestName` - Performance optimization for test name queries
- `IX_TestExecutionResults_SessionId` - Session-based grouping
- `IX_TestExecutionResults_Status` - Status-based filtering
- `IX_TestExecutionResults_CreatedAt` - Time-based queries

### PerformanceBenchmarks
Stores performance metrics and benchmarking data from various framework operations.

#### Columns
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | GUID | PRIMARY KEY | Unique identifier |
| OperationName | NVARCHAR(200) | NOT NULL, INDEX | Name of the benchmarked operation |
| Category | NVARCHAR(100) | NOT NULL | Performance category (Memory, CPU, Rendering) |
| Subcategory | NVARCHAR(100) | NULL | Performance subcategory for detailed classification |
| ExecutionTimeMs | REAL | NOT NULL | Operation execution time in milliseconds |
| MemoryUsageBytes | BIGINT | NULL | Memory usage during operation |
| CpuUsagePercent | REAL | NULL | CPU usage percentage |
| ThroughputOpsPerSecond | REAL | NULL | Operations per second throughput |
| Value | REAL | NOT NULL | Primary benchmark value |
| Unit | NVARCHAR(50) | NOT NULL | Unit of measurement |
| Description | NVARCHAR(500) | NULL | Detailed description of the benchmark |
| SessionId | GUID | NULL, INDEX | Session identifier for grouping |
| Environment | NVARCHAR(50) | NULL | Execution environment |
| MachineName | NVARCHAR(100) | NULL | Machine where benchmark was executed |
| FrameworkVersion | NVARCHAR(20) | NULL | Framework version |
| BuildVersion | NVARCHAR(50) | NULL | Build version |
| Tags | NVARCHAR(500) | NULL | Categorization tags |
| Notes | NTEXT | NULL | Additional notes and observations |
| CustomMetricsJson | NTEXT | NULL | JSON storage for complex metrics data |
| CreatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| UpdatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Last update timestamp |

#### Indexes
- `IX_PerformanceBenchmarks_OperationName` - Operation-based queries
- `IX_PerformanceBenchmarks_Category` - Category filtering
- `IX_PerformanceBenchmarks_SessionId` - Session grouping
- `IX_PerformanceBenchmarks_CreatedAt` - Time-based analysis

### ModuleHealthStatuses
Tracks health status and validation results for framework modules.

#### Columns
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | GUID | PRIMARY KEY | Unique identifier |
| ModuleName | NVARCHAR(100) | NOT NULL, INDEX | Name of the monitored module |
| Status | INT | NOT NULL | Health status (Healthy, Warning, Critical, Unknown) |
| LastCheckTime | DATETIME | NOT NULL | Timestamp of last health check |
| ValidationResults | NTEXT | NULL | Detailed validation results |
| Issues | NTEXT | NULL | JSON array of identified issues |
| Recommendations | NTEXT | NULL | JSON array of recommendations |
| PerformanceMetrics | NTEXT | NULL | JSON object with performance data |
| Dependencies | NTEXT | NULL | JSON array of module dependencies |
| Version | NVARCHAR(20) | NULL | Module version |
| Environment | NVARCHAR(50) | NULL | Environment context |
| SessionId | GUID | NULL, INDEX | Session identifier |
| CreatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| UpdatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Last update timestamp |

#### Indexes
- `IX_ModuleHealthStatuses_ModuleName` - Module-specific queries
- `IX_ModuleHealthStatuses_Status` - Status filtering
- `IX_ModuleHealthStatuses_LastCheckTime` - Time-based monitoring

### FrameworkConfigurations
Stores framework-wide configuration settings and preferences.

#### Columns
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | GUID | PRIMARY KEY | Unique identifier |
| ConfigurationKey | NVARCHAR(200) | NOT NULL, UNIQUE | Configuration key identifier |
| ConfigurationValue | NTEXT | NULL | Configuration value (JSON or string) |
| Category | NVARCHAR(100) | NOT NULL | Configuration category |
| Description | NVARCHAR(500) | NULL | Configuration description |
| IsSystemLevel | BOOLEAN | NOT NULL, DEFAULT FALSE | System vs user-level configuration |
| Environment | NVARCHAR(50) | NULL | Environment-specific configuration |
| Version | NVARCHAR(20) | NULL | Configuration version |
| CreatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| UpdatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Last update timestamp |

#### Indexes
- `IX_FrameworkConfigurations_ConfigurationKey` - Key-based lookups
- `IX_FrameworkConfigurations_Category` - Category filtering
- `IX_FrameworkConfigurations_IsSystemLevel` - System/user separation

### UserPreferences
Stores user-specific preferences and settings.

#### Columns
| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | GUID | PRIMARY KEY | Unique identifier |
| UserId | NVARCHAR(100) | NOT NULL, INDEX | User identifier |
| PreferenceKey | NVARCHAR(200) | NOT NULL | Preference key |
| PreferenceValue | NTEXT | NULL | Preference value (JSON or string) |
| Category | NVARCHAR(100) | NOT NULL | Preference category |
| Description | NVARCHAR(500) | NULL | Preference description |
| IsDefault | BOOLEAN | NOT NULL, DEFAULT FALSE | Whether this is a default value |
| CreatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Record creation timestamp |
| UpdatedAt | DATETIME | NOT NULL, DEFAULT CURRENT_TIMESTAMP | Last update timestamp |

#### Indexes
- `IX_UserPreferences_UserId` - User-specific queries
- `IX_UserPreferences_PreferenceKey` - Key-based lookups
- `IX_UserPreferences_Category` - Category filtering

## Data Relationships

### Foreign Key Constraints
Currently, the schema uses a denormalized approach with no explicit foreign key relationships to maintain flexibility and performance. Related data is linked through:
- `SessionId` fields for grouping related operations
- `ModuleName` references for module-specific data
- `UserId` for user-specific preferences

### Data Integrity
- All tables include audit timestamps (`CreatedAt`, `UpdatedAt`)
- Primary keys are GUIDs for distributed system compatibility
- Indexes are strategically placed for common query patterns
- JSON fields allow flexible storage of complex data structures

## Migration History

### Version 1.0 - Initial Schema (2025-06-08)
- Created all five core tables
- Established indexing strategy
- Implemented audit trail columns
- Added JSON support for flexible data storage

## Performance Considerations

### Indexing Strategy
- Primary indexes on frequently queried columns
- Composite indexes for common filter combinations
- Covering indexes for read-heavy operations

### Query Optimization
- Use parameterized queries to prevent SQL injection
- Implement pagination for large result sets
- Consider read replicas for reporting workloads

### Maintenance
- Regular index maintenance and statistics updates
- Automated cleanup of old data based on retention policies
- Monitoring of query performance and optimization

---

**Schema Version:** 1.0  
**Entity Framework Version:** 9.0.0  
**Database Provider:** SQLite  
**Last Migration:** InitialCreate (2025-06-08)
