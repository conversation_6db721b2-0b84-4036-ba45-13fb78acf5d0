using ArtDesignFramework.DataAccess;
using ArtDesignFramework.Performance.Analysis;
using ArtDesignFramework.Performance.Design;
using ArtDesignFramework.Performance.Memory;
using ArtDesignFramework.Performance.Monitoring;
using ArtDesignFramework.Performance.Rendering;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ArtDesignFramework.Performance;

/// <summary>
/// Extension methods for registering Performance module services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds Performance module services to the dependency injection container
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Optional configuration action</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPerformanceModule(
        this IServiceCollection services,
        Action<PerformanceOptions>? configureOptions = null)
    {
        // Configure options
        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }
        else
        {
            services.Configure<PerformanceOptions>(options => { });
        }

        // Register core performance monitoring services
        services.AddSingleton<IPerformanceMonitor, PerformanceMonitor>();
        services.AddSingleton<IMemoryProfiler, MemoryProfiler>();
        services.AddSingleton<IGarbageCollectionMonitor, GarbageCollectionMonitor>();

        // Register rendering performance services
        services.AddSingleton<IRenderingProfiler, RenderingProfiler>();
        services.AddSingleton<IGpuPerformanceMonitor, GpuPerformanceMonitor>();

        // Register design-specific performance services
        services.AddSingleton<ICanvasPerformanceMonitor, CanvasPerformanceMonitor>();
        services.AddSingleton<IGraphicsMemoryMonitor, GraphicsMemoryMonitor>();
        services.AddSingleton<IShaderPerformanceMonitor, ShaderPerformanceMonitor>();
        services.AddSingleton<ILargeFileOperationMonitor, LargeFileOperationMonitor>();

        // Register analysis services
        services.AddSingleton<IPerformanceAnalyzer, PerformanceAnalyzer>();
        services.AddSingleton<IOptimizationRecommendationEngine, OptimizationRecommendationEngine>();
        services.AddSingleton<IPerformanceBenchmark, PerformanceBenchmark>();

        // Register dashboard and reporting services
        services.AddSingleton<IPerformanceDashboard, PerformanceDashboard>();
        services.AddSingleton<IMetricsExporter, MetricsExporter>();
        services.AddSingleton<IPerformanceAlerts, PerformanceAlerts>();

        return services;
    }

    /// <summary>
    /// Adds Performance module hosted service for automatic initialization
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPerformanceHostedService(this IServiceCollection services)
    {
        services.AddHostedService<PerformanceHostedService>();
        return services;
    }

    /// <summary>
    /// Adds Performance module with full configuration
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Configuration action</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPerformance(
        this IServiceCollection services,
        Action<PerformanceOptions>? configureOptions = null)
    {
        return services
            .AddPerformanceModule(configureOptions)
            .AddPerformanceHostedService();
    }

    /// <summary>
    /// Adds Performance module with database integration enabled
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Optional configuration action</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPerformanceWithDatabase(
        this IServiceCollection services,
        Action<PerformanceOptions>? configureOptions = null)
    {
        // Add DataAccess module for database integration
        services.AddDataAccessModule(dataAccessOptions =>
        {
            dataAccessOptions.EnableAutomaticDatabaseCreation = true;
            dataAccessOptions.EnablePerformanceMonitoring = true;
            dataAccessOptions.DataRetentionDays = 30;
        });

        // Configure Performance options with database enabled
        return services.AddPerformance(options =>
        {
            options.EnableDatabaseStorage = true;
            options.DatabaseStorageInterval = TimeSpan.FromSeconds(30);
            configureOptions?.Invoke(options);
        });
    }

    /// <summary>
    /// Adds Performance module with legacy file-based storage only
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Optional configuration action</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPerformanceLegacy(
        this IServiceCollection services,
        Action<PerformanceOptions>? configureOptions = null)
    {
        return services.AddPerformance(options =>
        {
            options.EnableDatabaseStorage = false;
            options.EnableMetricsExport = true; // Enable file-based export instead
            configureOptions?.Invoke(options);
        });
    }
}

/// <summary>
/// Performance module configuration options
/// </summary>
public class PerformanceOptions
{
    /// <summary>
    /// Whether to enable automatic performance monitoring
    /// </summary>
    public bool EnableAutoMonitoring { get; set; } = true;

    /// <summary>
    /// Performance metrics collection interval
    /// </summary>
    public TimeSpan MetricsCollectionInterval { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Whether to enable memory profiling
    /// </summary>
    public bool EnableMemoryProfiling { get; set; } = true;

    /// <summary>
    /// Memory snapshot interval
    /// </summary>
    public TimeSpan MemorySnapshotInterval { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Whether to enable rendering performance monitoring
    /// </summary>
    public bool EnableRenderingProfiling { get; set; } = true;

    /// <summary>
    /// Whether to enable GPU performance monitoring
    /// </summary>
    public bool EnableGpuMonitoring { get; set; } = true;

    /// <summary>
    /// Whether to enable automatic bottleneck detection
    /// </summary>
    public bool EnableBottleneckDetection { get; set; } = true;

    /// <summary>
    /// Bottleneck detection interval
    /// </summary>
    public TimeSpan BottleneckDetectionInterval { get; set; } = TimeSpan.FromMinutes(1);

    /// <summary>
    /// Whether to enable performance alerts
    /// </summary>
    public bool EnablePerformanceAlerts { get; set; } = true;

    /// <summary>
    /// CPU usage threshold for alerts (percentage)
    /// </summary>
    public double CpuUsageAlertThreshold { get; set; } = 80.0;

    /// <summary>
    /// Memory usage threshold for alerts (percentage)
    /// </summary>
    public double MemoryUsageAlertThreshold { get; set; } = 85.0;

    /// <summary>
    /// Frame rate threshold for alerts (FPS)
    /// </summary>
    public double FrameRateAlertThreshold { get; set; } = 30.0;

    /// <summary>
    /// Whether to enable detailed operation tracking
    /// </summary>
    public bool EnableDetailedOperationTracking { get; set; } = true;

    /// <summary>
    /// Maximum number of operation statistics to keep
    /// </summary>
    public int MaxOperationStatistics { get; set; } = 1000;

    /// <summary>
    /// Whether to enable performance benchmarking
    /// </summary>
    public bool EnableBenchmarking { get; set; } = false;

    /// <summary>
    /// Benchmark execution interval
    /// </summary>
    public TimeSpan BenchmarkInterval { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// Whether to enable metrics export
    /// </summary>
    public bool EnableMetricsExport { get; set; } = false;

    /// <summary>
    /// Metrics export format
    /// </summary>
    public MetricsExportFormat MetricsExportFormat { get; set; } = MetricsExportFormat.Json;

    /// <summary>
    /// Metrics export interval
    /// </summary>
    public TimeSpan MetricsExportInterval { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Metrics export file path
    /// </summary>
    public string MetricsExportPath { get; set; } = "performance_metrics.json";

    /// <summary>
    /// Whether to enable real-time dashboard
    /// </summary>
    public bool EnableDashboard { get; set; } = false;

    /// <summary>
    /// Dashboard update interval
    /// </summary>
    public TimeSpan DashboardUpdateInterval { get; set; } = TimeSpan.FromSeconds(2);

    /// <summary>
    /// Maximum number of metrics history points to keep
    /// </summary>
    public int MaxMetricsHistoryPoints { get; set; } = 3600; // 1 hour at 1-second intervals

    /// <summary>
    /// Whether to enable garbage collection monitoring
    /// </summary>
    public bool EnableGcMonitoring { get; set; } = true;

    /// <summary>
    /// Whether to enable memory leak detection
    /// </summary>
    public bool EnableMemoryLeakDetection { get; set; } = true;

    /// <summary>
    /// Memory leak detection interval
    /// </summary>
    public TimeSpan MemoryLeakDetectionInterval { get; set; } = TimeSpan.FromMinutes(10);

    /// <summary>
    /// Whether to enable optimization recommendations
    /// </summary>
    public bool EnableOptimizationRecommendations { get; set; } = true;

    /// <summary>
    /// Optimization analysis interval
    /// </summary>
    public TimeSpan OptimizationAnalysisInterval { get; set; } = TimeSpan.FromMinutes(15);

    /// <summary>
    /// Performance data retention period
    /// </summary>
    public TimeSpan DataRetentionPeriod { get; set; } = TimeSpan.FromDays(7);

    /// <summary>
    /// Whether to enable performance logging
    /// </summary>
    public bool EnablePerformanceLogging { get; set; } = true;

    /// <summary>
    /// Performance log level threshold
    /// </summary>
    public PerformanceLogLevel LogLevelThreshold { get; set; } = PerformanceLogLevel.Warning;

    /// <summary>
    /// Whether to enable database storage for performance metrics
    /// </summary>
    public bool EnableDatabaseStorage { get; set; } = true;

    /// <summary>
    /// Database storage interval for performance metrics
    /// </summary>
    public TimeSpan DatabaseStorageInterval { get; set; } = TimeSpan.FromSeconds(30);
}

/// <summary>
/// Metrics export formats
/// </summary>
public enum MetricsExportFormat
{
    Json,
    Csv,
    Xml,
    Prometheus,
    InfluxDb
}

/// <summary>
/// Performance log levels
/// </summary>
public enum PerformanceLogLevel
{
    Debug,
    Information,
    Warning,
    Error,
    Critical
}

/// <summary>
/// Hosted service for Performance module initialization
/// </summary>
internal class PerformanceHostedService : IHostedService
{
    private readonly IPerformanceMonitor _performanceMonitor;
    private readonly IMemoryProfiler _memoryProfiler;
    private readonly IRenderingProfiler _renderingProfiler;
    private readonly IGpuPerformanceMonitor _gpuMonitor;
    private readonly ICanvasPerformanceMonitor _canvasMonitor;
    private readonly IGraphicsMemoryMonitor _graphicsMemoryMonitor;
    private readonly IShaderPerformanceMonitor _shaderMonitor;
    private readonly ILargeFileOperationMonitor _fileOperationMonitor;
    private readonly IPerformanceAlerts _performanceAlerts;
    private readonly PerformanceOptions _options;
    private readonly ILogger<PerformanceHostedService> _logger;

    public PerformanceHostedService(
        IPerformanceMonitor performanceMonitor,
        IMemoryProfiler memoryProfiler,
        IRenderingProfiler renderingProfiler,
        IGpuPerformanceMonitor gpuMonitor,
        ICanvasPerformanceMonitor canvasMonitor,
        IGraphicsMemoryMonitor graphicsMemoryMonitor,
        IShaderPerformanceMonitor shaderMonitor,
        ILargeFileOperationMonitor fileOperationMonitor,
        IPerformanceAlerts performanceAlerts,
        IOptions<PerformanceOptions> options,
        ILogger<PerformanceHostedService> logger)
    {
        _performanceMonitor = performanceMonitor;
        _memoryProfiler = memoryProfiler;
        _renderingProfiler = renderingProfiler;
        _gpuMonitor = gpuMonitor;
        _canvasMonitor = canvasMonitor;
        _graphicsMemoryMonitor = graphicsMemoryMonitor;
        _shaderMonitor = shaderMonitor;
        _fileOperationMonitor = fileOperationMonitor;
        _performanceAlerts = performanceAlerts;
        _options = options.Value;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting Performance module services");

        try
        {
            if (_options.EnableAutoMonitoring)
            {
                await _performanceMonitor.StartMonitoringAsync(cancellationToken);
            }

            if (_options.EnableMemoryProfiling)
            {
                await _memoryProfiler.StartMonitoringAsync(_options.MemorySnapshotInterval, cancellationToken);
            }

            if (_options.EnableRenderingProfiling)
            {
                await _renderingProfiler.StartMonitoringAsync(cancellationToken);
            }

            if (_options.EnableGpuMonitoring)
            {
                await _gpuMonitor.StartMonitoringAsync(cancellationToken);
            }

            // Start design-specific monitors
            await _canvasMonitor.StartMonitoringAsync(cancellationToken);
            await _graphicsMemoryMonitor.StartMonitoringAsync(cancellationToken);
            await _shaderMonitor.StartMonitoringAsync(cancellationToken);
            await _fileOperationMonitor.StartMonitoringAsync(cancellationToken);

            if (_options.EnablePerformanceAlerts)
            {
                await _performanceAlerts.StartMonitoringAsync(cancellationToken);
            }

            _logger.LogInformation("Performance module services started successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start Performance module services");
            throw;
        }
    }

    public async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping Performance module services");

        try
        {
            await _performanceMonitor.StopMonitoringAsync();
            await _memoryProfiler.StopMonitoringAsync();
            await _renderingProfiler.StopMonitoringAsync();
            await _gpuMonitor.StopMonitoringAsync();

            // Stop design-specific monitors
            await _canvasMonitor.StopMonitoringAsync();
            await _graphicsMemoryMonitor.StopMonitoringAsync();
            await _shaderMonitor.StopMonitoringAsync();
            await _fileOperationMonitor.StopMonitoringAsync();

            await _performanceAlerts.StopMonitoringAsync();

            _logger.LogInformation("Performance module services stopped successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping Performance module services");
        }
    }
}
