<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <!-- ImageHandling module specific settings -->
    <AssemblyTitle>ArtDesignFramework ImageHandling Module</AssemblyTitle>
    <AssemblyDescription>Image processing and manipulation for the ArtDesignFramework</AssemblyDescription>
  </PropertyGroup>

  <ItemGroup>
    <!-- Module-specific packages not provided by Directory.Build.props -->
    <PackageReference Include="SkiaSharp.NativeAssets.Linux" Version="2.88.8" />
    <PackageReference Include="SkiaSharp.HarfBuzz" Version="2.88.8" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.9" />
    <PackageReference Include="SixLabors.ImageSharp.Drawing" Version="2.1.0" />
    <PackageReference Include="MetadataExtractor" Version="2.8.1" />
    <PackageReference Include="Magick.NET-Q16-AnyCPU" Version="13.5.0" />
    <PackageReference Include="OpenCvSharp4" Version="4.8.0.20230708" />
    <PackageReference Include="OpenCvSharp4.runtime.win" Version="4.8.0.20230708" />
    <PackageReference Include="System.Reactive" Version="6.0.0" />
    <PackageReference Include="Polly" Version="8.2.0" />
    <PackageReference Include="BenchmarkDotNet" Version="0.13.12" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Core\ArtDesignFramework.Core.csproj" />
  </ItemGroup>

</Project>
