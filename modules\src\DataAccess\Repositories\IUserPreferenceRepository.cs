// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Entities;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository interface for user preference data access
/// </summary>
public interface IUserPreferenceRepository : IBaseRepository<UserPreference>
{
    /// <summary>
    /// Gets user preferences by user ID
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of user preferences</returns>
    Task<IEnumerable<UserPreference>> GetByUserAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user preference by user ID and key
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="key">Preference key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User preference if found, null otherwise</returns>
    Task<UserPreference?> GetByUserAndKeyAsync(string userId, string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user preference value by user ID and key
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="key">Preference key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Preference value if found, null otherwise</returns>
    Task<string?> GetValueAsync(string userId, string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user preference value by user ID and key with default fallback
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="key">Preference key</param>
    /// <param name="defaultValue">Default value if not found</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Preference value or default value</returns>
    Task<string> GetValueOrDefaultAsync(string userId, string key, string defaultValue, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user preferences by application
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="application">Application name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of user preferences</returns>
    Task<IEnumerable<UserPreference>> GetByApplicationAsync(string userId, string application, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user preferences by category
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="category">Category name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of user preferences</returns>
    Task<IEnumerable<UserPreference>> GetByCategoryAsync(string userId, string category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user preferences by module
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="moduleName">Module name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of user preferences</returns>
    Task<IEnumerable<UserPreference>> GetByModuleAsync(string userId, string moduleName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user preferences by component
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="componentName">Component name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of user preferences</returns>
    Task<IEnumerable<UserPreference>> GetByComponentAsync(string userId, string componentName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets synchronized user preferences
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of synchronized preferences</returns>
    Task<IEnumerable<UserPreference>> GetSynchronizedPreferencesAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets temporary user preferences
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of temporary preferences</returns>
    Task<IEnumerable<UserPreference>> GetTemporaryPreferencesAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets active user preferences
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of active preferences</returns>
    Task<IEnumerable<UserPreference>> GetActivePreferencesAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets expired user preferences
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of expired preferences</returns>
    Task<IEnumerable<UserPreference>> GetExpiredPreferencesAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets user preference value
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="key">Preference key</param>
    /// <param name="value">Preference value</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated user preference</returns>
    Task<UserPreference> SetValueAsync(string userId, string key, string value, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sets multiple user preference values
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="preferences">Dictionary of key-value pairs</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task SetValuesAsync(string userId, Dictionary<string, string> preferences, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes user preference by user ID and key
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="key">Preference key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if deleted, false if not found</returns>
    Task<bool> DeletePreferenceAsync(string userId, string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes all user preferences for a user
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of deleted preferences</returns>
    Task<int> DeleteAllUserPreferencesAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates preference access count and last accessed time
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="key">Preference key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Task representing the async operation</returns>
    Task UpdateAccessCountAsync(string userId, string key, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets user preference statistics
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>User preference statistics</returns>
    Task<UserPreferenceStatistics> GetUserStatisticsAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets most accessed user preferences
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="count">Number of preferences to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of most accessed preferences</returns>
    Task<IEnumerable<UserPreference>> GetMostAccessedPreferencesAsync(string userId, int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Exports user preferences to dictionary
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="application">Optional application filter</param>
    /// <param name="category">Optional category filter</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary of preference key-value pairs</returns>
    Task<Dictionary<string, string>> ExportPreferencesAsync(string userId, string? application = null, string? category = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Imports user preferences from dictionary
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="preferences">Dictionary of preference key-value pairs</param>
    /// <param name="application">Application name</param>
    /// <param name="overwriteExisting">Whether to overwrite existing preferences</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of imported preferences</returns>
    Task<int> ImportPreferencesAsync(string userId, Dictionary<string, string> preferences, string application, bool overwriteExisting = false, CancellationToken cancellationToken = default);

    /// <summary>
    /// Synchronizes user preferences across devices
    /// </summary>
    /// <param name="userId">User identifier</param>
    /// <param name="deviceId">Device identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of synchronized preferences</returns>
    Task<IEnumerable<UserPreference>> SynchronizePreferencesAsync(string userId, string deviceId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cleans up expired temporary preferences
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of cleaned up preferences</returns>
    Task<int> CleanupExpiredPreferencesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets users with preferences for a specific application
    /// </summary>
    /// <param name="application">Application name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of user IDs</returns>
    Task<IEnumerable<string>> GetUsersWithPreferencesAsync(string application, CancellationToken cancellationToken = default);
}

/// <summary>
/// User preference statistics
/// </summary>
public class UserPreferenceStatistics
{
    /// <summary>
    /// Gets or sets the user identifier
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the total number of preferences
    /// </summary>
    public int TotalPreferences { get; set; }

    /// <summary>
    /// Gets or sets the number of active preferences
    /// </summary>
    public int ActivePreferences { get; set; }

    /// <summary>
    /// Gets or sets the number of synchronized preferences
    /// </summary>
    public int SynchronizedPreferences { get; set; }

    /// <summary>
    /// Gets or sets the number of temporary preferences
    /// </summary>
    public int TemporaryPreferences { get; set; }

    /// <summary>
    /// Gets or sets the total access count across all preferences
    /// </summary>
    public long TotalAccessCount { get; set; }

    /// <summary>
    /// Gets or sets the average access count per preference
    /// </summary>
    public double AverageAccessCount { get; set; }

    /// <summary>
    /// Gets or sets the most accessed preference key
    /// </summary>
    public string? MostAccessedKey { get; set; }

    /// <summary>
    /// Gets or sets the access count of the most accessed preference
    /// </summary>
    public long MostAccessedCount { get; set; }

    /// <summary>
    /// Gets or sets the last access time
    /// </summary>
    public DateTime? LastAccessTime { get; set; }

    /// <summary>
    /// Gets or sets the number of applications with preferences
    /// </summary>
    public int ApplicationCount { get; set; }

    /// <summary>
    /// Gets or sets the number of categories with preferences
    /// </summary>
    public int CategoryCount { get; set; }
}
