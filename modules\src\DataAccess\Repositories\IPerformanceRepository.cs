// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Entities;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository interface for performance benchmark data access
/// </summary>
public interface IPerformanceRepository : IBaseRepository<PerformanceBenchmark>
{
    /// <summary>
    /// Gets performance benchmarks by operation name
    /// </summary>
    /// <param name="operationName">Operation name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of performance benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetByOperationAsync(string operationName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance benchmarks by category
    /// </summary>
    /// <param name="category">Category name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of performance benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance benchmarks by session ID
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of performance benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetBySessionAsync(Guid sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance benchmarks within a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of performance benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance benchmarks by environment
    /// </summary>
    /// <param name="environment">Environment name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of performance benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets benchmarks that met their performance targets
    /// </summary>
    /// <param name="metTarget">Whether the benchmark met its target</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of performance benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetByTargetStatusAsync(bool metTarget, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets benchmarks with execution time above threshold
    /// </summary>
    /// <param name="thresholdMs">Execution time threshold in milliseconds</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of slow benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetSlowBenchmarksAsync(double thresholdMs, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets benchmarks with memory usage above threshold
    /// </summary>
    /// <param name="thresholdBytes">Memory usage threshold in bytes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of memory-intensive benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetMemoryIntensiveBenchmarksAsync(long thresholdBytes, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance statistics for an operation
    /// </summary>
    /// <param name="operationName">Operation name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Performance statistics</returns>
    Task<PerformanceStatistics> GetOperationStatisticsAsync(string operationName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance statistics for a category
    /// </summary>
    /// <param name="category">Category name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Performance statistics</returns>
    Task<PerformanceStatistics> GetCategoryStatisticsAsync(string category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance statistics for a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Performance statistics</returns>
    Task<PerformanceStatistics> GetStatisticsByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets recent benchmarks for a specific operation
    /// </summary>
    /// <param name="operationName">Operation name</param>
    /// <param name="count">Number of recent benchmarks to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of recent benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetRecentBenchmarksAsync(string operationName, int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets performance trend data for an operation
    /// </summary>
    /// <param name="operationName">Operation name</param>
    /// <param name="days">Number of days to analyze</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of performance trend data</returns>
    Task<IEnumerable<PerformanceTrendData>> GetPerformanceTrendAsync(string operationName, int days = 30, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets regression benchmarks
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of regression benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetRegressionBenchmarksAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets automated benchmarks
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of automated benchmarks</returns>
    Task<IEnumerable<PerformanceBenchmark>> GetAutomatedBenchmarksAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes old benchmarks based on retention policy
    /// </summary>
    /// <param name="retentionDays">Number of days to retain</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of deleted records</returns>
    Task<int> CleanupOldBenchmarksAsync(int retentionDays, CancellationToken cancellationToken = default);
}

/// <summary>
/// Performance statistics
/// </summary>
public class PerformanceStatistics
{
    /// <summary>
    /// Gets or sets the total number of benchmarks
    /// </summary>
    public int TotalBenchmarks { get; set; }

    /// <summary>
    /// Gets or sets the average execution time in milliseconds
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the minimum execution time in milliseconds
    /// </summary>
    public double MinExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the maximum execution time in milliseconds
    /// </summary>
    public double MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the median execution time in milliseconds
    /// </summary>
    public double MedianExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the 95th percentile execution time in milliseconds
    /// </summary>
    public double P95ExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the average memory usage in bytes
    /// </summary>
    public long AverageMemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the peak memory usage in bytes
    /// </summary>
    public long PeakMemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the average throughput (operations per second)
    /// </summary>
    public double AverageThroughputOps { get; set; }

    /// <summary>
    /// Gets or sets the number of benchmarks that met their targets
    /// </summary>
    public int BenchmarksMetTarget { get; set; }

    /// <summary>
    /// Gets the percentage of benchmarks that met their targets
    /// </summary>
    public double TargetMetPercentage => TotalBenchmarks > 0 ? (double)BenchmarksMetTarget / TotalBenchmarks * 100 : 0;
}

/// <summary>
/// Performance trend data point
/// </summary>
public class PerformanceTrendData
{
    /// <summary>
    /// Gets or sets the date of the data point
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// Gets or sets the average execution time for the date
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the average memory usage for the date
    /// </summary>
    public long AverageMemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the number of benchmarks for the date
    /// </summary>
    public int BenchmarkCount { get; set; }

    /// <summary>
    /// Gets or sets the percentage improvement compared to baseline
    /// </summary>
    public double? ImprovementPercentage { get; set; }
}
