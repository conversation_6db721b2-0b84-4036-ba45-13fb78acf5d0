// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Configuration;
using ArtDesignFramework.DataAccess.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository implementation for module health status data access
/// </summary>
public class ModuleHealthRepository : BaseRepository<ModuleHealthStatus>, IModuleHealthRepository
{
    public ModuleHealthRepository(ArtDesignFrameworkDbContext context, ILogger<ModuleHealthRepository> logger)
        : base(context, logger) { }

    public async Task<IEnumerable<ModuleHealthStatus>> GetByModuleAsync(string moduleName, CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.ModuleName == moduleName).OrderByDescending(m => m.CreatedAt).ToListAsync(cancellationToken);

    public async Task<ModuleHealthStatus?> GetLatestByModuleAsync(string moduleName, CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.ModuleName == moduleName).OrderByDescending(m => m.CreatedAt).FirstOrDefaultAsync(cancellationToken);

    public async Task<IEnumerable<ModuleHealthStatus>> GetByStatusAsync(string status, CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.CurrentStatus == status).OrderByDescending(m => m.CreatedAt).ToListAsync(cancellationToken);

    public async Task<IEnumerable<ModuleHealthStatus>> GetBySessionAsync(Guid sessionId, CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.SessionId == sessionId).OrderBy(m => m.CreatedAt).ToListAsync(cancellationToken);

    public async Task<IEnumerable<ModuleHealthStatus>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.CreatedAt >= startDate && m.CreatedAt <= endDate).OrderByDescending(m => m.CreatedAt).ToListAsync(cancellationToken);

    public async Task<IEnumerable<ModuleHealthStatus>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.Environment == environment).OrderByDescending(m => m.CreatedAt).ToListAsync(cancellationToken);

    public async Task<IEnumerable<ModuleHealthStatus>> GetUnhealthyModulesAsync(double threshold = 0.8, CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.HealthScore < threshold).OrderBy(m => m.HealthScore).ToListAsync(cancellationToken);

    public async Task<IEnumerable<ModuleHealthStatus>> GetModulesWithBuildFailuresAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(m => !m.BuildsSuccessfully).OrderByDescending(m => m.CreatedAt).ToListAsync(cancellationToken);

    public async Task<IEnumerable<ModuleHealthStatus>> GetModulesWithFailingTestsAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.FailingTests > 0).OrderByDescending(m => m.FailingTests).ToListAsync(cancellationToken);

    public async Task<IEnumerable<ModuleHealthStatus>> GetModulesWithPhantomImplementationsAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.HasPhantomImplementations).OrderByDescending(m => m.PhantomImplementationCount).ToListAsync(cancellationToken);

    public async Task<IEnumerable<ModuleHealthStatus>> GetModulesWithCriticalIssuesAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.CriticalIssues > 0).OrderByDescending(m => m.CriticalIssues).ToListAsync(cancellationToken);

    public async Task<ModuleHealthStatistics> GetHealthStatisticsAsync(CancellationToken cancellationToken = default)
    {
        var latestStatuses = await DbSet.GroupBy(m => m.ModuleName)
            .Select(g => g.OrderByDescending(m => m.CreatedAt).First())
            .ToListAsync(cancellationToken);

        return new ModuleHealthStatistics
        {
            TotalModules = latestStatuses.Count,
            HealthyModules = latestStatuses.Count(m => m.HealthScore >= 0.8),
            UnhealthyModules = latestStatuses.Count(m => m.HealthScore < 0.8),
            AverageHealthScore = latestStatuses.Any() ? latestStatuses.Average(m => m.HealthScore) : 0,
            ModulesWithBuildFailures = latestStatuses.Count(m => !m.BuildsSuccessfully),
            ModulesWithFailingTests = latestStatuses.Count(m => m.FailingTests > 0),
            ModulesWithPhantomImplementations = latestStatuses.Count(m => m.HasPhantomImplementations),
            ModulesWithCriticalIssues = latestStatuses.Count(m => m.CriticalIssues > 0)
        };
    }

    public async Task<IEnumerable<HealthTrendData>> GetHealthTrendAsync(string moduleName, int days = 30, CancellationToken cancellationToken = default)
    {
        var startDate = DateTime.UtcNow.AddDays(-days);
        var statuses = await DbSet.Where(m => m.ModuleName == moduleName && m.CreatedAt >= startDate)
            .OrderBy(m => m.CreatedAt)
            .ToListAsync(cancellationToken);

        return statuses.GroupBy(s => s.CreatedAt.Date)
            .Select(g => new HealthTrendData
            {
                Date = g.Key,
                HealthScore = g.Average(s => s.HealthScore),
                PassingTests = g.Sum(s => s.PassingTests),
                FailingTests = g.Sum(s => s.FailingTests),
                BuildSuccessful = g.Any(s => s.BuildsSuccessfully),
                CriticalIssues = g.Sum(s => s.CriticalIssues)
            })
            .OrderBy(t => t.Date);
    }

    public async Task<IEnumerable<ModuleHealthStatus>> GetModulesNeedingValidationAsync(CancellationToken cancellationToken = default)
    {
        var oneDayAgo = DateTime.UtcNow.AddDays(-1);
        return await DbSet.Where(m => m.NextValidationAt <= DateTime.UtcNow || m.ValidatedAt < oneDayAgo)
            .OrderBy(m => m.NextValidationAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<ModuleHealthStatus>> GetAutomatedValidationResultsAsync(CancellationToken cancellationToken = default)
        => await DbSet.Where(m => m.IsAutomatedValidation).OrderByDescending(m => m.CreatedAt).ToListAsync(cancellationToken);

    public async Task<HealthComparisonData?> GetHealthComparisonAsync(string moduleName, DateTime date1, DateTime date2, CancellationToken cancellationToken = default)
    {
        var status1 = await DbSet.Where(m => m.ModuleName == moduleName && m.CreatedAt.Date == date1.Date)
            .OrderByDescending(m => m.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        var status2 = await DbSet.Where(m => m.ModuleName == moduleName && m.CreatedAt.Date == date2.Date)
            .OrderByDescending(m => m.CreatedAt)
            .FirstOrDefaultAsync(cancellationToken);

        if (status1 == null || status2 == null)
            return null;

        return new HealthComparisonData
        {
            ModuleName = moduleName,
            HealthScore1 = status1.HealthScore,
            HealthScore2 = status2.HealthScore,
            TestCount1 = status1.TotalTests,
            TestCount2 = status2.TotalTests
        };
    }

    public async Task<int> CleanupOldHealthStatusesAsync(int retentionDays, int keepLatestPerModule = 5, CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
        var statusesToDelete = new List<ModuleHealthStatus>();

        var moduleGroups = await DbSet.Where(m => m.CreatedAt < cutoffDate)
            .GroupBy(m => m.ModuleName)
            .ToListAsync(cancellationToken);

        foreach (var group in moduleGroups)
        {
            var statusesToKeep = group.OrderByDescending(m => m.CreatedAt).Take(keepLatestPerModule);
            var statusesToRemove = group.Except(statusesToKeep);
            statusesToDelete.AddRange(statusesToRemove);
        }

        if (statusesToDelete.Any())
        {
            DbSet.RemoveRange(statusesToDelete);
            await Context.SaveChangesAsync(cancellationToken);
        }

        return statusesToDelete.Count;
    }
}
