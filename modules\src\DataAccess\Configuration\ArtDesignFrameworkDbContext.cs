// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.DataAccess.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ArtDesignFramework.DataAccess.Configuration;

/// <summary>
/// Main Entity Framework DbContext for the ArtDesignFramework
/// </summary>
public class ArtDesignFrameworkDbContext : DbContext
{
    private readonly DataAccessOptions _options;
    private readonly ILogger<ArtDesignFrameworkDbContext>? _logger;

    /// <summary>
    /// Initializes a new instance of the ArtDesignFrameworkDbContext class
    /// </summary>
    /// <param name="options">DbContext options</param>
    public ArtDesignFrameworkDbContext(DbContextOptions<ArtDesignFrameworkDbContext> options) : base(options)
    {
        _options = new DataAccessOptions();
    }

    /// <summary>
    /// Initializes a new instance of the ArtDesignFrameworkDbContext class with configuration options
    /// </summary>
    /// <param name="options">DbContext options</param>
    /// <param name="dataAccessOptions">DataAccess configuration options</param>
    /// <param name="logger">Logger instance</param>
    public ArtDesignFrameworkDbContext(
        DbContextOptions<ArtDesignFrameworkDbContext> options,
        IOptions<DataAccessOptions> dataAccessOptions,
        ILogger<ArtDesignFrameworkDbContext>? logger = null) : base(options)
    {
        _options = dataAccessOptions?.Value ?? new DataAccessOptions();
        _logger = logger;
    }

    #region DbSet Properties

    /// <summary>
    /// Gets or sets the test execution results
    /// </summary>
    public DbSet<TestExecutionResult> TestExecutionResults { get; set; } = null!;

    /// <summary>
    /// Gets or sets the performance benchmarks
    /// </summary>
    public DbSet<PerformanceBenchmark> PerformanceBenchmarks { get; set; } = null!;

    /// <summary>
    /// Gets or sets the module health statuses
    /// </summary>
    public DbSet<ModuleHealthStatus> ModuleHealthStatuses { get; set; } = null!;

    /// <summary>
    /// Gets or sets the framework configurations
    /// </summary>
    public DbSet<FrameworkConfiguration> FrameworkConfigurations { get; set; } = null!;

    /// <summary>
    /// Gets or sets the user preferences
    /// </summary>
    public DbSet<UserPreference> UserPreferences { get; set; } = null!;

    #endregion

    /// <summary>
    /// Configures the database connection and options
    /// </summary>
    /// <param name="optionsBuilder">Options builder</param>
    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            var connectionString = GetConnectionString();
            optionsBuilder.UseSqlite(connectionString, options =>
            {
                options.CommandTimeout(_options.CommandTimeoutSeconds);
            });

            // Configure SQLite-specific settings
            ConfigureSqliteOptions(optionsBuilder);
        }

        base.OnConfiguring(optionsBuilder);
    }

    /// <summary>
    /// Configures entity relationships and constraints
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure TestExecutionResult entity
        ConfigureTestExecutionResult(modelBuilder);

        // Configure PerformanceBenchmark entity
        ConfigurePerformanceBenchmark(modelBuilder);

        // Configure ModuleHealthStatus entity
        ConfigureModuleHealthStatus(modelBuilder);

        // Configure FrameworkConfiguration entity
        ConfigureFrameworkConfiguration(modelBuilder);

        // Configure UserPreference entity
        ConfigureUserPreference(modelBuilder);

        // Apply global configurations
        ApplyGlobalConfigurations(modelBuilder);
    }

    /// <summary>
    /// Saves changes to the database with automatic timestamp updates
    /// </summary>
    /// <returns>Number of affected rows</returns>
    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    /// <summary>
    /// Saves changes to the database asynchronously with automatic timestamp updates
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of affected rows</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Ensures the database is created and initialized
    /// </summary>
    /// <returns>True if database was created, false if it already existed</returns>
    public async Task<bool> EnsureDatabaseCreatedAsync()
    {
        try
        {
            _logger?.LogInformation("Ensuring database is created at: {ConnectionString}", GetConnectionString());

            // Ensure data directory exists
            var dataDirectory = Path.Combine("L:", "framework", "data");
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory);
                _logger?.LogInformation("Created data directory: {DataDirectory}", dataDirectory);
            }

            var created = await Database.EnsureCreatedAsync();

            if (created)
            {
                _logger?.LogInformation("Database created successfully");
                await SeedInitialDataAsync();
            }
            else
            {
                _logger?.LogDebug("Database already exists");
            }

            return created;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to ensure database creation");
            throw;
        }
    }

    /// <summary>
    /// Gets the connection string for the database
    /// </summary>
    /// <returns>SQLite connection string</returns>
    private string GetConnectionString()
    {
        if (!string.IsNullOrEmpty(_options.ConnectionString))
        {
            return _options.ConnectionString;
        }

        var dataDirectory = Path.Combine("L:", "framework", "data");
        var databasePath = Path.Combine(dataDirectory, "ArtDesignFramework.db");

        return $"Data Source={databasePath}";
    }

    /// <summary>
    /// Configures SQLite-specific options
    /// </summary>
    /// <param name="optionsBuilder">Options builder</param>
    private void ConfigureSqliteOptions(DbContextOptionsBuilder optionsBuilder)
    {
        // Enable sensitive data logging in development
        if (_options.EnableSensitiveDataLogging)
        {
            optionsBuilder.EnableSensitiveDataLogging();
        }

        // Enable detailed errors
        optionsBuilder.EnableDetailedErrors();

        // Configure logging
        if (_logger != null)
        {
            optionsBuilder.UseLoggerFactory(LoggerFactory.Create(builder =>
                builder.SetMinimumLevel(LogLevel.Information)));
        }

        // Enable service provider caching
        optionsBuilder.EnableServiceProviderCaching();

        // Configure retry on failure if enabled
        if (_options.EnableRetryOnFailure)
        {
            // Note: SQLite doesn't support retry policies like SQL Server
            // This is a placeholder for future enhancement
        }
    }

    /// <summary>
    /// Updates timestamps for entities being saved
    /// </summary>
    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.Entity is BaseEntity entity)
            {
                if (entry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.UtcNow;
                }
                entity.UpdatedAt = DateTime.UtcNow;
            }
            else
            {
                // Handle entities that don't inherit from BaseEntity
                var createdAtProperty = entry.Properties.FirstOrDefault(p => p.Metadata.Name == "CreatedAt");
                var updatedAtProperty = entry.Properties.FirstOrDefault(p => p.Metadata.Name == "UpdatedAt");

                if (entry.State == EntityState.Added && createdAtProperty != null)
                {
                    createdAtProperty.CurrentValue = DateTime.UtcNow;
                }

                if (updatedAtProperty != null)
                {
                    updatedAtProperty.CurrentValue = DateTime.UtcNow;
                }
            }
        }
    }

    /// <summary>
    /// Seeds initial data into the database
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    private async Task SeedInitialDataAsync()
    {
        try
        {
            _logger?.LogInformation("Seeding initial data");

            // Seed default framework configurations
            await SeedFrameworkConfigurationsAsync();

            await SaveChangesAsync();

            _logger?.LogInformation("Initial data seeding completed");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to seed initial data");
            throw;
        }
    }

    /// <summary>
    /// Seeds default framework configurations
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    private async Task SeedFrameworkConfigurationsAsync()
    {
        var existingConfigs = await FrameworkConfigurations.AnyAsync();
        if (existingConfigs)
        {
            return; // Already seeded
        }

        var defaultConfigs = new[]
        {
            new FrameworkConfiguration
            {
                ConfigurationKey = "Database.RetentionDays",
                ConfigurationValue = "30",
                DataType = "Integer",
                DefaultValue = "30",
                Category = "Database",
                Description = "Number of days to retain data before cleanup",
                Scope = "Global",
                Environment = "All"
            },
            new FrameworkConfiguration
            {
                ConfigurationKey = "Performance.EnableMonitoring",
                ConfigurationValue = "true",
                DataType = "Boolean",
                DefaultValue = "true",
                Category = "Performance",
                Description = "Enable performance monitoring and benchmarking",
                IsFeatureFlag = true,
                Scope = "Global",
                Environment = "All"
            },
            new FrameworkConfiguration
            {
                ConfigurationKey = "TestFramework.AutoValidation",
                ConfigurationValue = "true",
                DataType = "Boolean",
                DefaultValue = "true",
                Category = "Testing",
                Description = "Enable automatic test validation and health monitoring",
                IsFeatureFlag = true,
                Scope = "Global",
                Environment = "All"
            }
        };

        await FrameworkConfigurations.AddRangeAsync(defaultConfigs);
    }

    #region Entity Configurations

    /// <summary>
    /// Configures the TestExecutionResult entity
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    private static void ConfigureTestExecutionResult(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<TestExecutionResult>();

        // Configure indexes for performance
        entity.HasIndex(e => e.TestName);
        entity.HasIndex(e => e.TestSuite);
        entity.HasIndex(e => e.Passed);
        entity.HasIndex(e => e.CreatedAt);
        entity.HasIndex(e => e.SessionId);
        entity.HasIndex(e => new { e.TestSuite, e.TestName, e.CreatedAt });

        // Configure properties
        entity.Property(e => e.TestName).IsRequired().HasMaxLength(500);
        entity.Property(e => e.TestSuite).IsRequired().HasMaxLength(200);
        entity.Property(e => e.ErrorMessage).HasMaxLength(2000);
        entity.Property(e => e.TestCategory).HasMaxLength(100);
        entity.Property(e => e.Priority).HasMaxLength(50);
        entity.Property(e => e.Environment).HasMaxLength(100);
        entity.Property(e => e.MachineName).HasMaxLength(100);
        entity.Property(e => e.FrameworkVersion).HasMaxLength(50);
        entity.Property(e => e.TestRunnerVersion).HasMaxLength(50);
        entity.Property(e => e.BuildVersion).HasMaxLength(100);
        entity.Property(e => e.Tags).HasMaxLength(1000);
        entity.Property(e => e.Notes).HasMaxLength(2000);

        // Configure JSON columns
        entity.Property(e => e.ErrorDetails).HasColumnType("TEXT");
        entity.Property(e => e.TestCode).HasColumnType("TEXT");
        entity.Property(e => e.TestParametersJson).HasColumnType("TEXT");
        entity.Property(e => e.ContextJson).HasColumnType("TEXT");
        entity.Property(e => e.CustomMetricsJson).HasColumnType("TEXT");
    }

    /// <summary>
    /// Configures the PerformanceBenchmark entity
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    private static void ConfigurePerformanceBenchmark(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<PerformanceBenchmark>();

        // Configure indexes for performance
        entity.HasIndex(e => e.OperationName);
        entity.HasIndex(e => e.Category);
        entity.HasIndex(e => e.CreatedAt);
        entity.HasIndex(e => e.SessionId);
        entity.HasIndex(e => new { e.Category, e.OperationName, e.CreatedAt });

        // Configure properties
        entity.Property(e => e.OperationName).IsRequired().HasMaxLength(500);
        entity.Property(e => e.Category).IsRequired().HasMaxLength(200);
        entity.Property(e => e.Subcategory).HasMaxLength(200);
        entity.Property(e => e.Unit).HasMaxLength(50);
        entity.Property(e => e.Description).HasMaxLength(1000);
        entity.Property(e => e.TestDataSize).HasMaxLength(200);
        entity.Property(e => e.RenderingQuality).HasMaxLength(100);
        entity.Property(e => e.Environment).HasMaxLength(100);
        entity.Property(e => e.MachineName).HasMaxLength(100);
        entity.Property(e => e.FrameworkVersion).HasMaxLength(50);
        entity.Property(e => e.BuildVersion).HasMaxLength(100);
        entity.Property(e => e.Tags).HasMaxLength(1000);
        entity.Property(e => e.Notes).HasMaxLength(2000);

        // Configure JSON columns
        entity.Property(e => e.CustomMetricsJson).HasColumnType("TEXT");
    }

    /// <summary>
    /// Configures the ModuleHealthStatus entity
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    private static void ConfigureModuleHealthStatus(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<ModuleHealthStatus>();

        // Configure indexes for performance
        entity.HasIndex(e => e.ModuleName);
        entity.HasIndex(e => e.CurrentStatus);
        entity.HasIndex(e => e.CreatedAt);
        entity.HasIndex(e => e.SessionId);
        entity.HasIndex(e => new { e.ModuleName, e.CreatedAt });

        // Configure properties
        entity.Property(e => e.ModuleName).IsRequired().HasMaxLength(200);
        entity.Property(e => e.Version).HasMaxLength(50);
        entity.Property(e => e.CurrentStatus).IsRequired().HasMaxLength(50);
        entity.Property(e => e.PreviousStatus).HasMaxLength(50);
        entity.Property(e => e.ClaimedStatus).HasMaxLength(100);
        entity.Property(e => e.Environment).HasMaxLength(100);
        entity.Property(e => e.MachineName).HasMaxLength(100);
        entity.Property(e => e.FrameworkVersion).HasMaxLength(50);
        entity.Property(e => e.BuildVersion).HasMaxLength(100);
        entity.Property(e => e.Tags).HasMaxLength(1000);
        entity.Property(e => e.Notes).HasMaxLength(2000);

        // Configure JSON columns
        entity.Property(e => e.ValidationIssuesJson).HasColumnType("TEXT");
        entity.Property(e => e.RecommendationsJson).HasColumnType("TEXT");
        entity.Property(e => e.DependenciesJson).HasColumnType("TEXT");
        entity.Property(e => e.PerformanceMetricsJson).HasColumnType("TEXT");
        entity.Property(e => e.SecurityScanResultsJson).HasColumnType("TEXT");
    }

    /// <summary>
    /// Configures the FrameworkConfiguration entity
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    private static void ConfigureFrameworkConfiguration(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<FrameworkConfiguration>();

        // Configure indexes for performance
        entity.HasIndex(e => e.ConfigurationKey).IsUnique();
        entity.HasIndex(e => e.Category);
        entity.HasIndex(e => e.Scope);
        entity.HasIndex(e => e.Environment);
        entity.HasIndex(e => e.IsActive);
        entity.HasIndex(e => new { e.Category, e.ConfigurationKey });
        entity.HasIndex(e => new { e.Scope, e.Environment, e.ConfigurationKey });

        // Configure properties
        entity.Property(e => e.ConfigurationKey).IsRequired().HasMaxLength(200);
        entity.Property(e => e.DataType).IsRequired().HasMaxLength(50);
        entity.Property(e => e.Category).IsRequired().HasMaxLength(100);
        entity.Property(e => e.Subcategory).HasMaxLength(100);
        entity.Property(e => e.Description).HasMaxLength(1000);
        entity.Property(e => e.Scope).IsRequired().HasMaxLength(50);
        entity.Property(e => e.ModuleName).HasMaxLength(200);
        entity.Property(e => e.UserId).HasMaxLength(100);
        entity.Property(e => e.MachineName).HasMaxLength(100);
        entity.Property(e => e.Environment).HasMaxLength(100);
        entity.Property(e => e.FrameworkVersion).HasMaxLength(50);
        entity.Property(e => e.Source).HasMaxLength(100);
        entity.Property(e => e.CreatedBy).HasMaxLength(100);
        entity.Property(e => e.ModifiedBy).HasMaxLength(100);
        entity.Property(e => e.Tags).HasMaxLength(1000);
        entity.Property(e => e.Notes).HasMaxLength(2000);
        entity.Property(e => e.ValidationPattern).HasMaxLength(500);
        entity.Property(e => e.ValidationErrorMessage).HasMaxLength(500);

        // Configure JSON columns
        entity.Property(e => e.ConfigurationValue).HasColumnType("TEXT");
        entity.Property(e => e.DefaultValue).HasColumnType("TEXT");
        entity.Property(e => e.MinValue).HasColumnType("TEXT");
        entity.Property(e => e.MaxValue).HasColumnType("TEXT");
        entity.Property(e => e.AllowedValuesJson).HasColumnType("TEXT");
        entity.Property(e => e.MetadataJson).HasColumnType("TEXT");
        entity.Property(e => e.ChangeHistoryJson).HasColumnType("TEXT");
    }

    /// <summary>
    /// Configures the UserPreference entity
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    private static void ConfigureUserPreference(ModelBuilder modelBuilder)
    {
        var entity = modelBuilder.Entity<UserPreference>();

        // Configure indexes for performance
        entity.HasIndex(e => e.UserId);
        entity.HasIndex(e => e.PreferenceKey);
        entity.HasIndex(e => e.Application);
        entity.HasIndex(e => e.Category);
        entity.HasIndex(e => e.IsActive);
        entity.HasIndex(e => new { e.UserId, e.PreferenceKey }).IsUnique();
        entity.HasIndex(e => new { e.Application, e.UserId, e.Category });

        // Configure properties
        entity.Property(e => e.UserId).IsRequired().HasMaxLength(100);
        entity.Property(e => e.PreferenceKey).IsRequired().HasMaxLength(200);
        entity.Property(e => e.DataType).IsRequired().HasMaxLength(50);
        entity.Property(e => e.Category).IsRequired().HasMaxLength(100);
        entity.Property(e => e.Subcategory).HasMaxLength(100);
        entity.Property(e => e.Description).HasMaxLength(1000);
        entity.Property(e => e.Application).IsRequired().HasMaxLength(200);
        entity.Property(e => e.ModuleName).HasMaxLength(200);
        entity.Property(e => e.ComponentName).HasMaxLength(200);
        entity.Property(e => e.Scope).IsRequired().HasMaxLength(50);
        entity.Property(e => e.MachineName).HasMaxLength(100);
        entity.Property(e => e.Environment).HasMaxLength(100);
        entity.Property(e => e.FrameworkVersion).HasMaxLength(50);
        entity.Property(e => e.ApplicationVersion).HasMaxLength(50);
        entity.Property(e => e.UserDisplayName).HasMaxLength(200);
        entity.Property(e => e.UserEmail).HasMaxLength(200);
        entity.Property(e => e.UserRole).HasMaxLength(100);
        entity.Property(e => e.DeviceId).HasMaxLength(200);
        entity.Property(e => e.DeviceType).HasMaxLength(50);
        entity.Property(e => e.OperatingSystem).HasMaxLength(100);
        entity.Property(e => e.BrowserInfo).HasMaxLength(200);
        entity.Property(e => e.Tags).HasMaxLength(1000);
        entity.Property(e => e.Notes).HasMaxLength(2000);

        // Configure JSON columns
        entity.Property(e => e.PreferenceValue).HasColumnType("TEXT");
        entity.Property(e => e.DefaultValue).HasColumnType("TEXT");
        entity.Property(e => e.MetadataJson).HasColumnType("TEXT");
        entity.Property(e => e.ChangeHistoryJson).HasColumnType("TEXT");
        entity.Property(e => e.UsageStatisticsJson).HasColumnType("TEXT");
    }

    /// <summary>
    /// Applies global configurations to all entities
    /// </summary>
    /// <param name="modelBuilder">Model builder</param>
    private static void ApplyGlobalConfigurations(ModelBuilder modelBuilder)
    {
        // Configure all DateTime properties to use UTC
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                {
                    property.SetColumnType("TEXT");
                }
            }
        }

        // Configure all GUID properties
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(Guid) || property.ClrType == typeof(Guid?))
                {
                    property.SetColumnType("TEXT");
                }
            }
        }
    }

    #endregion
}
