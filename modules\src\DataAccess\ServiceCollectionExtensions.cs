// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.DataAccess.Configuration;
using ArtDesignFramework.DataAccess.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.DataAccess;

/// <summary>
/// Extension methods for registering DataAccess module services
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds DataAccess module services to the dependency injection container
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Optional configuration action</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddDataAccessModule(
        this IServiceCollection services,
        Action<DataAccessOptions>? configureOptions = null)
    {
        // Configure options
        if (configureOptions != null)
        {
            services.Configure(configureOptions);
        }
        else
        {
            services.Configure<DataAccessOptions>(options => { });
        }

        // Register DbContext with SQLite
        services.AddDbContext<ArtDesignFrameworkDbContext>((serviceProvider, options) =>
        {
            var dataAccessOptions = serviceProvider.GetService<Microsoft.Extensions.Options.IOptions<DataAccessOptions>>();
            var logger = serviceProvider.GetService<ILogger<ArtDesignFrameworkDbContext>>();

            var dataDirectory = Path.Combine("L:", "framework", "data");
            Directory.CreateDirectory(dataDirectory);
            var connectionString = $"Data Source={Path.Combine(dataDirectory, "ArtDesignFramework.db")}";

            options.UseSqlite(connectionString, sqliteOptions =>
            {
                sqliteOptions.CommandTimeout(dataAccessOptions?.Value?.CommandTimeoutSeconds ?? 30);
            });

            if (dataAccessOptions?.Value?.EnableSensitiveDataLogging == true)
            {
                options.EnableSensitiveDataLogging();
            }

            options.EnableServiceProviderCaching(true);
            options.EnableDetailedErrors();
        });

        // Register database initializer
        services.AddScoped<DatabaseInitializer>();

        // Register repository interfaces and implementations
        services.AddScoped<ITestResultRepository, TestResultRepository>();
        services.AddScoped<IPerformanceRepository, PerformanceRepository>();
        services.AddScoped<IModuleHealthRepository, ModuleHealthRepository>();
        services.AddScoped<IConfigurationRepository, ConfigurationRepository>();
        services.AddScoped<IUserPreferenceRepository, UserPreferenceRepository>();

        return services;
    }

    /// <summary>
    /// Adds DataAccess module with full configuration
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Configuration action</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddDataAccess(
        this IServiceCollection services,
        Action<DataAccessOptions>? configureOptions = null)
    {
        return services.AddDataAccessModule(configureOptions);
    }
}

/// <summary>
/// DataAccess module configuration options
/// </summary>
public class DataAccessOptions
{
    /// <summary>
    /// Gets or sets the database connection string
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets whether to enable automatic database creation
    /// </summary>
    public bool EnableAutomaticDatabaseCreation { get; set; } = true;

    /// <summary>
    /// Gets or sets whether to enable automatic migrations
    /// </summary>
    public bool EnableAutomaticMigrations { get; set; } = true;

    /// <summary>
    /// Gets or sets whether to enable sensitive data logging
    /// </summary>
    public bool EnableSensitiveDataLogging { get; set; } = false;

    /// <summary>
    /// Gets or sets the command timeout in seconds
    /// </summary>
    public int CommandTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Gets or sets whether to enable retry on failure
    /// </summary>
    public bool EnableRetryOnFailure { get; set; } = true;

    /// <summary>
    /// Gets or sets the maximum retry count
    /// </summary>
    public int MaxRetryCount { get; set; } = 3;

    /// <summary>
    /// Gets or sets the data retention period in days
    /// </summary>
    public int DataRetentionDays { get; set; } = 30;

    /// <summary>
    /// Gets or sets whether to enable performance monitoring
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;
}

// ArtDesignFrameworkDbContext will be implemented in the next task
