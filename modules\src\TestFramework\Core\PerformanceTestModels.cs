using System;
using System.Collections.Generic;

namespace ArtDesignFramework.TestFramework.Core
{
    /// <summary>
    /// Performance test result model for comprehensive testing infrastructure
    /// Last Updated: 2025-01-09 23:55:00 UTC
    /// </summary>
    public class PerformanceTestResult
    {
        /// <summary>
        /// Gets or sets the test name
        /// </summary>
        public string TestName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the test category
        /// </summary>
        public string TestCategory { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the test was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets the test start time
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the test end time
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the test duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Gets or sets the actual measured value
        /// </summary>
        public double ActualValue { get; set; }

        /// <summary>
        /// Gets or sets the expected value
        /// </summary>
        public double ExpectedValue { get; set; }

        /// <summary>
        /// Gets or sets the memory reduction percentage (for memory tests)
        /// </summary>
        public double MemoryReductionPercentage { get; set; }

        /// <summary>
        /// Gets or sets the performance improvement percentage (for performance tests)
        /// </summary>
        public double PerformanceImprovementPercentage { get; set; }

        /// <summary>
        /// Gets or sets the error message if the test failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets additional test metrics
        /// </summary>
        public Dictionary<string, object> Metrics { get; set; } = new();

        /// <summary>
        /// Gets or sets test notes or additional information
        /// </summary>
        public string? Notes { get; set; }
    }

    /// <summary>
    /// AI system validation test result
    /// Last Updated: 2025-01-09 23:55:00 UTC
    /// </summary>
    public class AIValidationTestResult
    {
        /// <summary>
        /// Gets or sets the test name
        /// </summary>
        public string TestName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the test was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets the test start time
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the test end time
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the test duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Gets or sets the number of suggestions generated
        /// </summary>
        public int SuggestionsGenerated { get; set; }

        /// <summary>
        /// Gets or sets the number of high-confidence suggestions
        /// </summary>
        public int HighConfidenceSuggestions { get; set; }

        /// <summary>
        /// Gets or sets the average confidence score
        /// </summary>
        public double AverageConfidenceScore { get; set; }

        /// <summary>
        /// Gets or sets the response time in milliseconds
        /// </summary>
        public double ResponseTimeMs { get; set; }

        /// <summary>
        /// Gets or sets the error message if the test failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets additional validation metrics
        /// </summary>
        public Dictionary<string, object> ValidationMetrics { get; set; } = new();
    }

    /// <summary>
    /// Selection tools test result
    /// Last Updated: 2025-01-09 23:55:00 UTC
    /// </summary>
    public class SelectionToolsTestResult
    {
        /// <summary>
        /// Gets or sets the test name
        /// </summary>
        public string TestName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the test was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets the test start time
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the test end time
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the test duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Gets or sets the selection tool type tested
        /// </summary>
        public string SelectionToolType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the selection accuracy percentage
        /// </summary>
        public double AccuracyPercentage { get; set; }

        /// <summary>
        /// Gets or sets the selection performance in milliseconds
        /// </summary>
        public double PerformanceMs { get; set; }

        /// <summary>
        /// Gets or sets whether the selection was within performance targets
        /// </summary>
        public bool WithinPerformanceTarget { get; set; }

        /// <summary>
        /// Gets or sets the error message if the test failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets additional selection metrics
        /// </summary>
        public Dictionary<string, object> SelectionMetrics { get; set; } = new();
    }

    /// <summary>
    /// GPU acceleration test result
    /// Last Updated: 2025-01-09 23:55:00 UTC
    /// </summary>
    public class GPUAccelerationTestResult
    {
        /// <summary>
        /// Gets or sets the test name
        /// </summary>
        public string TestName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the test was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets the test start time
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the test end time
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the test duration
        /// </summary>
        public TimeSpan Duration { get; set; }

        /// <summary>
        /// Gets or sets whether GPU acceleration is available
        /// </summary>
        public bool GPUAvailable { get; set; }

        /// <summary>
        /// Gets or sets the CPU rendering time in milliseconds
        /// </summary>
        public double CPURenderTimeMs { get; set; }

        /// <summary>
        /// Gets or sets the GPU rendering time in milliseconds
        /// </summary>
        public double GPURenderTimeMs { get; set; }

        /// <summary>
        /// Gets or sets the performance improvement percentage
        /// </summary>
        public double PerformanceImprovementPercentage { get; set; }

        /// <summary>
        /// Gets or sets the GPU device information
        /// </summary>
        public string? GPUDeviceInfo { get; set; }

        /// <summary>
        /// Gets or sets the error message if the test failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Gets or sets additional GPU metrics
        /// </summary>
        public Dictionary<string, object> GPUMetrics { get; set; } = new();
    }

    /// <summary>
    /// Comprehensive test suite result aggregating all test results
    /// Last Updated: 2025-01-09 23:55:00 UTC
    /// </summary>
    public class ComprehensiveTestSuiteResult
    {
        /// <summary>
        /// Gets or sets the test suite name
        /// </summary>
        public string TestSuiteName { get; set; } = "Comprehensive Testing Infrastructure";

        /// <summary>
        /// Gets or sets the test suite start time
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// Gets or sets the test suite end time
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// Gets or sets the total test suite duration
        /// </summary>
        public TimeSpan TotalDuration { get; set; }

        /// <summary>
        /// Gets or sets whether all tests passed
        /// </summary>
        public bool AllTestsPassed { get; set; }

        /// <summary>
        /// Gets or sets the total number of tests executed
        /// </summary>
        public int TotalTests { get; set; }

        /// <summary>
        /// Gets or sets the number of tests that passed
        /// </summary>
        public int TestsPassed { get; set; }

        /// <summary>
        /// Gets or sets the number of tests that failed
        /// </summary>
        public int TestsFailed { get; set; }

        /// <summary>
        /// Gets or sets the performance test results
        /// </summary>
        public List<PerformanceTestResult> PerformanceTestResults { get; set; } = new();

        /// <summary>
        /// Gets or sets the AI validation test results
        /// </summary>
        public List<AIValidationTestResult> AIValidationResults { get; set; } = new();

        /// <summary>
        /// Gets or sets the selection tools test results
        /// </summary>
        public List<SelectionToolsTestResult> SelectionToolsResults { get; set; } = new();

        /// <summary>
        /// Gets or sets the GPU acceleration test results
        /// </summary>
        public List<GPUAccelerationTestResult> GPUAccelerationResults { get; set; } = new();

        /// <summary>
        /// Gets or sets the overall test suite score (0-100)
        /// </summary>
        public double OverallScore { get; set; }

        /// <summary>
        /// Gets or sets any critical errors encountered during testing
        /// </summary>
        public List<string> CriticalErrors { get; set; } = new();

        /// <summary>
        /// Gets or sets performance regression warnings
        /// </summary>
        public List<string> PerformanceRegressionWarnings { get; set; } = new();

        /// <summary>
        /// Gets or sets test summary notes
        /// </summary>
        public string? SummaryNotes { get; set; }
    }
}
