Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🎨 Dynamic Canvas Comprehensive Testing" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📦 Installing dependencies..." -ForegroundColor Yellow
npm install

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install dependencies" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🧪 Running comprehensive functional tests..." -ForegroundColor Yellow
npm run test:comprehensive

Write-Host ""
Write-Host "📊 Running automated test suite with reporting..." -ForegroundColor Yellow
npm run test:automated

Write-Host ""
Write-Host "📈 Generating coverage report..." -ForegroundColor Yellow
npm run test:coverage

Write-Host ""
Write-Host "✅ Testing complete! Check the test-reports directory for detailed results." -ForegroundColor Green

# Open the test reports directory if it exists
$reportsDir = "test-reports"
if (Test-Path $reportsDir) {
    Write-Host "📂 Opening test reports directory..." -ForegroundColor Cyan
    Invoke-Item $reportsDir
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
