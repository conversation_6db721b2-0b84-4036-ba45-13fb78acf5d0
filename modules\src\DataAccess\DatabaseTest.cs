// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.DataAccess.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.DataAccess;

/// <summary>
/// Simple test class to verify database creation and functionality
/// </summary>
public class DatabaseTest
{
    /// <summary>
    /// Tests database creation and basic operations
    /// </summary>
    /// <returns>Task representing the async operation</returns>
    public static async Task TestDatabaseCreationAsync()
    {
        // Set up services
        var services = new ServiceCollection();

        // Add logging
        services.AddLogging(builder => builder.SetMinimumLevel(LogLevel.Information));

        // Add DataAccess module
        services.AddDataAccess(options =>
        {
            options.EnableAutomaticDatabaseCreation = true;
            options.EnableSensitiveDataLogging = false;
            options.DataRetentionDays = 30;
        });

        var serviceProvider = services.BuildServiceProvider();

        try
        {
            // Get the database context
            using var scope = serviceProvider.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ArtDesignFrameworkDbContext>();
            var initializer = scope.ServiceProvider.GetRequiredService<DatabaseInitializer>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<DatabaseTest>>();

            logger.LogInformation("Starting database test");

            // Initialize the database
            await initializer.InitializeAsync();

            // Test database creation
            var created = await context.EnsureDatabaseCreatedAsync();
            logger.LogInformation("Database creation result: {Created}", created);

            // Test basic operations
            await TestBasicOperationsAsync(context, logger);

            logger.LogInformation("Database test completed successfully");
        }
        catch (Exception ex)
        {
            var logger = serviceProvider.GetRequiredService<ILogger<DatabaseTest>>();
            logger.LogError(ex, "Database test failed");
            throw;
        }
        finally
        {
            await serviceProvider.DisposeAsync();
        }
    }

    /// <summary>
    /// Tests basic database operations
    /// </summary>
    /// <param name="context">Database context</param>
    /// <param name="logger">Logger instance</param>
    /// <returns>Task representing the async operation</returns>
    private static async Task TestBasicOperationsAsync(ArtDesignFrameworkDbContext context, ILogger logger)
    {
        logger.LogInformation("Testing basic database operations");

        // Test framework configuration
        var config = new Entities.FrameworkConfiguration
        {
            ConfigurationKey = "Test.DatabaseConnection",
            ConfigurationValue = "true",
            DataType = "Boolean",
            Category = "Testing",
            Description = "Test configuration for database connectivity",
            Scope = "Global",
            Environment = "Development"
        };

        context.FrameworkConfigurations.Add(config);
        await context.SaveChangesAsync();

        logger.LogInformation("Added test configuration: {Key}", config.ConfigurationKey);

        // Test user preference
        var preference = new Entities.UserPreference
        {
            UserId = "test-user",
            PreferenceKey = "Test.Theme",
            PreferenceValue = "Dark",
            DataType = "String",
            Category = "UI",
            Application = "ArtDesignFramework",
            Description = "Test user preference for theme"
        };

        context.UserPreferences.Add(preference);
        await context.SaveChangesAsync();

        logger.LogInformation("Added test user preference: {Key}", preference.PreferenceKey);

        // Verify data was saved
        var savedConfig = await context.FrameworkConfigurations
            .FirstOrDefaultAsync(c => c.ConfigurationKey == "Test.DatabaseConnection");

        var savedPreference = await context.UserPreferences
            .FirstOrDefaultAsync(p => p.PreferenceKey == "Test.Theme");

        if (savedConfig == null || savedPreference == null)
        {
            throw new InvalidOperationException("Failed to save or retrieve test data");
        }

        logger.LogInformation("Successfully verified data persistence");

        // Clean up test data
        context.FrameworkConfigurations.Remove(savedConfig);
        context.UserPreferences.Remove(savedPreference);
        await context.SaveChangesAsync();

        logger.LogInformation("Test data cleaned up successfully");
    }
}
