// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Logging;
using ArtDesignFramework.TestFramework.Core;
using ArtDesignFramework.DataAccess;
using ArtDesignFramework.DataAccess.Repositories;

namespace ArtDesignFramework.TestFramework;

/// <summary>
/// Extension methods for configuring TestFramework services with database integration
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds TestFramework services to the dependency injection container
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configureOptions">Optional configuration action</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddTestFramework(
        this IServiceCollection services,
        Action<TestFrameworkOptions>? configureOptions = null)
    {
        ArgumentNullException.ThrowIfNull(services);

        // Configure options
        var options = new TestFrameworkOptions();
        configureOptions?.Invoke(options);
        services.Configure<TestFrameworkOptions>(opt =>
        {
            opt.EnableDatabaseStorage = options.EnableDatabaseStorage;
            opt.EnableFileBasedReports = options.EnableFileBasedReports;
            opt.DatabaseStorageEnabled = options.DatabaseStorageEnabled;
            opt.ValidationFrequencyHours = options.ValidationFrequencyHours;
            opt.PreventFalseStatusClaims = options.PreventFalseStatusClaims;
            opt.GenerateHealthDashboards = options.GenerateHealthDashboards;
            opt.ValidatePhantomImplementations = options.ValidatePhantomImplementations;
            opt.EnforceEnterpriseStandards = options.EnforceEnterpriseStandards;
        });

        // Register core TestFramework services
        services.TryAddSingleton<ModuleHealthMonitor>();
        services.TryAddSingleton<AutomatedBuildValidator>();
        services.TryAddSingleton<EnterpriseTestOrchestrator>();
        services.TryAddSingleton<TestRunner>();

        // Register configuration
        services.TryAddSingleton(new AutomatedValidationConfig
        {
            PreventFalseStatusClaims = options.PreventFalseStatusClaims,
            GenerateHealthDashboards = options.GenerateHealthDashboards,
            ValidatePhantomImplementations = options.ValidatePhantomImplementations,
            EnforceEnterpriseStandards = options.EnforceEnterpriseStandards,
            ValidationFrequencyHours = options.ValidationFrequencyHours
        });

        // Add database integration if enabled
        if (options.EnableDatabaseStorage)
        {
            services.AddDataAccessModule(dataAccessOptions =>
            {
                dataAccessOptions.EnableAutomaticDatabaseCreation = true;
                dataAccessOptions.EnablePerformanceMonitoring = true;
                dataAccessOptions.DataRetentionDays = 30;
            });
        }

        return services;
    }

    /// <summary>
    /// Adds TestFramework services with database integration enabled by default
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddTestFrameworkWithDatabase(this IServiceCollection services)
    {
        return services.AddTestFramework(options =>
        {
            options.EnableDatabaseStorage = true;
            options.EnableFileBasedReports = true; // Maintain backward compatibility
            options.DatabaseStorageEnabled = true;
        });
    }

    /// <summary>
    /// Adds TestFramework services with file-based reports only (legacy mode)
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddTestFrameworkLegacy(this IServiceCollection services)
    {
        return services.AddTestFramework(options =>
        {
            options.EnableDatabaseStorage = false;
            options.EnableFileBasedReports = true;
            options.DatabaseStorageEnabled = false;
        });
    }
}

/// <summary>
/// Configuration options for TestFramework module
/// </summary>
public class TestFrameworkOptions
{
    /// <summary>
    /// Gets or sets whether to enable database storage for test results
    /// </summary>
    public bool EnableDatabaseStorage { get; set; } = true;

    /// <summary>
    /// Gets or sets whether to enable file-based reports (for backward compatibility)
    /// </summary>
    public bool EnableFileBasedReports { get; set; } = true;

    /// <summary>
    /// Gets or sets whether database storage is enabled (legacy property)
    /// </summary>
    public bool DatabaseStorageEnabled { get; set; } = true;

    /// <summary>
    /// Gets or sets the validation frequency in hours
    /// </summary>
    public int ValidationFrequencyHours { get; set; } = 24;

    /// <summary>
    /// Gets or sets whether to prevent false status claims
    /// </summary>
    public bool PreventFalseStatusClaims { get; set; } = true;

    /// <summary>
    /// Gets or sets whether to generate health dashboards
    /// </summary>
    public bool GenerateHealthDashboards { get; set; } = true;

    /// <summary>
    /// Gets or sets whether to validate phantom implementations
    /// </summary>
    public bool ValidatePhantomImplementations { get; set; } = true;

    /// <summary>
    /// Gets or sets whether to enforce enterprise standards
    /// </summary>
    public bool EnforceEnterpriseStandards { get; set; } = true;
}
