using System.Collections.Concurrent;
using System.Diagnostics;
using System.Reactive.Subjects;
using ArtDesignFramework.DataAccess.Entities;
using ArtDesignFramework.DataAccess.Repositories;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace ArtDesignFramework.Performance.Memory;

/// <summary>
/// Memory profiler implementation
/// </summary>
public class MemoryProfiler : IMemoryProfiler, IDisposable
{
    private readonly ILogger<MemoryProfiler> _logger;
    private readonly ConcurrentQueue<MemoryUsageInfo> _usageHistory = new();
    private readonly List<MemorySnapshot> _snapshots = new();
    private readonly Timer? _monitoringTimer;
    private readonly object _lockObject = new();
    private readonly IPerformanceRepository? _performanceRepository;
    private readonly PerformanceOptions _options;
    private readonly Timer? _databaseStorageTimer;

    private bool _isMonitoring;
    private bool _disposed;

    /// <summary>
    /// Initializes a new instance of the MemoryProfiler class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    /// <param name="options">Performance options</param>
    /// <param name="performanceRepository">Optional performance repository for database storage</param>
    public MemoryProfiler(
        ILogger<MemoryProfiler> logger,
        IOptions<PerformanceOptions>? options = null,
        IPerformanceRepository? performanceRepository = null)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? new PerformanceOptions();
        _performanceRepository = performanceRepository;
        _monitoringTimer = new Timer(CollectMemoryUsage, null, Timeout.Infinite, Timeout.Infinite);

        // Initialize database storage timer if enabled
        if (_options.EnableDatabaseStorage && _performanceRepository != null)
        {
            _databaseStorageTimer = new Timer(StoreMemoryDataInDatabase, null, Timeout.Infinite, Timeout.Infinite);
        }
    }

    /// <inheritdoc />
    public MemoryUsageInfo GetCurrentUsage()
    {
        var process = Process.GetCurrentProcess();
        var gcInfo = GC.GetGCMemoryInfo();

        return new MemoryUsageInfo
        {
            Timestamp = DateTime.UtcNow,
            TotalAllocatedBytes = GC.GetTotalAllocatedBytes(),
            WorkingSetBytes = process.WorkingSet64,
            PrivateMemoryBytes = process.PrivateMemorySize64,
            VirtualMemoryBytes = process.VirtualMemorySize64,
            ManagedHeapBytes = GC.GetTotalMemory(false),
            Gen0HeapBytes = gcInfo.GenerationInfo.Length > 0 ? gcInfo.GenerationInfo[0].SizeAfterBytes : 0,
            Gen1HeapBytes = gcInfo.GenerationInfo.Length > 1 ? gcInfo.GenerationInfo[1].SizeAfterBytes : 0,
            Gen2HeapBytes = gcInfo.GenerationInfo.Length > 2 ? gcInfo.GenerationInfo[2].SizeAfterBytes : 0,
            LargeObjectHeapBytes = gcInfo.GenerationInfo.Length > 3 ? gcInfo.GenerationInfo[3].SizeAfterBytes : 0,
            PinnedObjectHeapBytes = gcInfo.PinnedObjectsCount,
            PressureLevel = DetermineMemoryPressureLevel(gcInfo)
        };
    }

    /// <inheritdoc />
    public MemorySnapshot TakeSnapshot(string name)
    {
        var usage = GetCurrentUsage();
        var snapshot = new MemorySnapshot
        {
            Name = name,
            Timestamp = DateTime.UtcNow,
            Usage = usage
        };

        // Collect object type information (simplified implementation)
        CollectObjectTypeInfo(snapshot);

        lock (_lockObject)
        {
            _snapshots.Add(snapshot);
        }

        _logger.LogInformation("Memory snapshot '{SnapshotName}' taken. Memory usage: {MemoryUsage:N0} bytes",
            name, usage.TotalAllocatedBytes);

        return snapshot;
    }

    /// <inheritdoc />
    public MemoryComparison CompareSnapshots(MemorySnapshot before, MemorySnapshot after)
    {
        var comparison = new MemoryComparison
        {
            Before = before,
            After = after,
            MemoryDifferenceBytes = after.Usage.TotalAllocatedBytes - before.Usage.TotalAllocatedBytes
        };

        // Compare object types
        foreach (var afterType in after.ObjectTypes)
        {
            if (before.ObjectTypes.TryGetValue(afterType.Key, out var beforeType))
            {
                var countDiff = afterType.Value.InstanceCount - beforeType.InstanceCount;
                if (countDiff != 0)
                {
                    comparison.ObjectCountDifferences[afterType.Key] = countDiff;
                }
            }
            else
            {
                comparison.NewObjectTypes.Add(afterType.Key);
            }
        }

        // Find removed types
        foreach (var beforeType in before.ObjectTypes.Keys)
        {
            if (!after.ObjectTypes.ContainsKey(beforeType))
            {
                comparison.RemovedObjectTypes.Add(beforeType);
            }
        }

        return comparison;
    }

    /// <inheritdoc />
    public async Task StartMonitoringAsync(TimeSpan interval, CancellationToken cancellationToken = default)
    {
        if (_isMonitoring)
            return;

        _logger.LogInformation("Starting memory monitoring with interval: {Interval}", interval);

        _isMonitoring = true;
        _monitoringTimer?.Change(TimeSpan.Zero, interval);

        // Start database storage timer if enabled (store every 5 minutes to avoid overwhelming database)
        if (_options.EnableDatabaseStorage && _databaseStorageTimer != null)
        {
            _databaseStorageTimer.Change(TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }

        await Task.CompletedTask;
    }

    /// <inheritdoc />
    public async Task StopMonitoringAsync()
    {
        if (!_isMonitoring)
            return;

        _logger.LogInformation("Stopping memory monitoring");

        _isMonitoring = false;
        _monitoringTimer?.Change(Timeout.Infinite, Timeout.Infinite);
        _databaseStorageTimer?.Change(Timeout.Infinite, Timeout.Infinite);

        await Task.CompletedTask;
    }

    /// <inheritdoc />
    public IEnumerable<MemoryUsageInfo> GetUsageHistory(TimeSpan duration)
    {
        var cutoff = DateTime.UtcNow - duration;
        return _usageHistory.Where(usage => usage.Timestamp >= cutoff).ToList();
    }

    /// <inheritdoc />
    public MemoryLeakAnalysis DetectMemoryLeaks()
    {
        var analysis = new MemoryLeakAnalysis();

        // Simple leak detection based on memory growth
        var recentUsage = GetUsageHistory(TimeSpan.FromHours(1)).ToList();
        if (recentUsage.Count < 2)
        {
            analysis.ConfidenceLevel = 0;
            return analysis;
        }

        var firstUsage = recentUsage.First();
        var lastUsage = recentUsage.Last();
        var timeDiff = lastUsage.Timestamp - firstUsage.Timestamp;
        var memoryGrowth = lastUsage.TotalAllocatedBytes - firstUsage.TotalAllocatedBytes;

        if (timeDiff.TotalHours > 0)
        {
            analysis.MemoryGrowthRatePerHour = memoryGrowth / timeDiff.TotalHours;
        }

        // Determine confidence based on growth rate and consistency
        if (analysis.MemoryGrowthRatePerHour > 100 * 1024 * 1024) // 100MB per hour
        {
            analysis.ConfidenceLevel = 0.8;
            analysis.Suspects.Add(new MemoryLeakSuspect
            {
                TypeName = "Unknown",
                GrowthRate = analysis.MemoryGrowthRatePerHour,
                MemoryImpactBytes = memoryGrowth,
                Confidence = 0.8,
                SuspectedCause = "Consistent memory growth detected"
            });
        }

        return analysis;
    }

    /// <inheritdoc />
    public long ForceGarbageCollection()
    {
        var beforeCollection = GC.GetTotalMemory(false);

        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();

        var afterCollection = GC.GetTotalMemory(false);
        var freedMemory = beforeCollection - afterCollection;

        _logger.LogInformation("Forced garbage collection freed {FreedMemory:N0} bytes", freedMemory);

        return freedMemory;
    }

    /// <inheritdoc />
    public AllocationPatternAnalysis GetAllocationPatterns()
    {
        // Simplified implementation
        var analysis = new AllocationPatternAnalysis();

        var recentUsage = GetUsageHistory(TimeSpan.FromMinutes(10)).ToList();
        if (recentUsage.Count > 1)
        {
            var totalAllocations = recentUsage.Last().TotalAllocatedBytes - recentUsage.First().TotalAllocatedBytes;
            var timeSpan = recentUsage.Last().Timestamp - recentUsage.First().Timestamp;

            if (timeSpan.TotalSeconds > 0)
            {
                analysis.AllocationRatePerSecond = totalAllocations / timeSpan.TotalSeconds;
            }
        }

        return analysis;
    }

    private async void StoreMemoryDataInDatabase(object? state)
    {
        if (!_isMonitoring || _disposed || _performanceRepository == null)
            return;

        try
        {
            _logger.LogDebug("💾 Storing memory profiling data in database");

            var sessionId = Guid.NewGuid();
            var currentUsage = GetCurrentUsage();

            // Store current memory usage
            var memoryBenchmark = new PerformanceBenchmark
            {
                OperationName = "Memory Profiling",
                Category = "Memory",
                Subcategory = "Usage",
                ExecutionTimeMs = 0, // Not applicable for memory metrics
                MemoryUsageBytes = currentUsage.TotalAllocatedBytes,
                CpuUsagePercent = 0, // Not tracked in memory profiler
                ThroughputOpsPerSecond = 0, // Not applicable
                Value = currentUsage.TotalAllocatedBytes / (1024.0 * 1024), // MB
                Unit = "Megabytes",
                Description = "Memory usage profiling data",
                SessionId = sessionId,
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                MachineName = Environment.MachineName,
                FrameworkVersion = "2.0.0",
                BuildVersion = "2.0.0",
                Tags = "memory,profiling,usage",
                Notes = $"Working Set: {currentUsage.WorkingSetBytes / (1024.0 * 1024):F1}MB, Managed Heap: {currentUsage.ManagedHeapBytes / (1024.0 * 1024):F1}MB",
                CustomMetricsJson = System.Text.Json.JsonSerializer.Serialize(new
                {
                    TotalAllocatedBytes = currentUsage.TotalAllocatedBytes,
                    WorkingSetBytes = currentUsage.WorkingSetBytes,
                    PrivateMemoryBytes = currentUsage.PrivateMemoryBytes,
                    VirtualMemoryBytes = currentUsage.VirtualMemoryBytes,
                    ManagedHeapBytes = currentUsage.ManagedHeapBytes,
                    Gen0HeapBytes = currentUsage.Gen0HeapBytes,
                    Gen1HeapBytes = currentUsage.Gen1HeapBytes,
                    Gen2HeapBytes = currentUsage.Gen2HeapBytes,
                    LargeObjectHeapBytes = currentUsage.LargeObjectHeapBytes,
                    PinnedObjectHeapBytes = currentUsage.PinnedObjectHeapBytes,
                    PressureLevel = currentUsage.PressureLevel.ToString()
                })
            };

            await _performanceRepository.AddAsync(memoryBenchmark);

            // Store memory leak analysis if available
            var leakAnalysis = DetectMemoryLeaks();
            if (leakAnalysis.ConfidenceLevel > 0.5)
            {
                var leakBenchmark = new PerformanceBenchmark
                {
                    OperationName = "Memory Leak Detection",
                    Category = "Memory",
                    Subcategory = "LeakAnalysis",
                    ExecutionTimeMs = 0,
                    MemoryUsageBytes = (long)leakAnalysis.MemoryGrowthRatePerHour,
                    CpuUsagePercent = 0,
                    ThroughputOpsPerSecond = 0,
                    Value = leakAnalysis.ConfidenceLevel * 100,
                    Unit = "Percent",
                    Description = "Memory leak detection analysis",
                    SessionId = sessionId,
                    Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                    MachineName = Environment.MachineName,
                    FrameworkVersion = "2.0.0",
                    BuildVersion = "2.0.0",
                    Tags = "memory,leak,analysis",
                    Notes = $"Confidence: {leakAnalysis.ConfidenceLevel:P}, Growth Rate: {leakAnalysis.MemoryGrowthRatePerHour / (1024.0 * 1024):F1}MB/hour",
                    CustomMetricsJson = System.Text.Json.JsonSerializer.Serialize(new
                    {
                        ConfidenceLevel = leakAnalysis.ConfidenceLevel,
                        MemoryGrowthRatePerHour = leakAnalysis.MemoryGrowthRatePerHour,
                        Suspects = leakAnalysis.Suspects.Select(s => new
                        {
                            s.TypeName,
                            s.GrowthRate,
                            s.MemoryImpactBytes,
                            s.Confidence,
                            s.SuspectedCause
                        })
                    })
                };

                await _performanceRepository.AddAsync(leakBenchmark);
            }

            _logger.LogDebug("✅ Memory profiling data stored in database successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Failed to store memory profiling data in database");
            // Don't throw - database storage failure shouldn't break memory profiling
        }
    }

    private void CollectMemoryUsage(object? state)
    {
        if (!_isMonitoring || _disposed)
            return;

        try
        {
            var usage = GetCurrentUsage();
            _usageHistory.Enqueue(usage);

            // Keep only recent history (last hour)
            while (_usageHistory.Count > 3600) // Assuming 1-second intervals
            {
                _usageHistory.TryDequeue(out _);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting memory usage");
        }
    }

    private static MemoryPressureLevel DetermineMemoryPressureLevel(GCMemoryInfo gcInfo)
    {
        var memoryLoadBytes = gcInfo.MemoryLoadBytes;
        var totalAvailableBytes = gcInfo.TotalAvailableMemoryBytes;

        if (totalAvailableBytes == 0)
            return MemoryPressureLevel.Low;

        var usagePercentage = (double)memoryLoadBytes / totalAvailableBytes;

        return usagePercentage switch
        {
            > 0.9 => MemoryPressureLevel.Critical,
            > 0.8 => MemoryPressureLevel.High,
            > 0.6 => MemoryPressureLevel.Medium,
            _ => MemoryPressureLevel.Low
        };
    }

    private static void CollectObjectTypeInfo(MemorySnapshot snapshot)
    {
        // Simplified object type collection
        // In a real implementation, this would use more sophisticated profiling
        snapshot.ObjectTypes["System.String"] = new ObjectTypeInfo
        {
            TypeName = "System.String",
            InstanceCount = 1000, // Placeholder
            TotalSizeBytes = 50000 // Placeholder
        };

        snapshot.ObjectTypes["System.Object[]"] = new ObjectTypeInfo
        {
            TypeName = "System.Object[]",
            InstanceCount = 500, // Placeholder
            TotalSizeBytes = 25000 // Placeholder
        };
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _monitoringTimer?.Dispose();
        _databaseStorageTimer?.Dispose();

        _logger.LogInformation("Memory profiler disposed");
    }
}

/// <summary>
/// Garbage collection monitor implementation
/// </summary>
public class GarbageCollectionMonitor : IGarbageCollectionMonitor, IDisposable
{
    private readonly ILogger<GarbageCollectionMonitor> _logger;
    private readonly Subject<GcEvent> _gcEventsSubject = new();
    private bool _isMonitoring;
    private bool _disposed;

    /// <summary>
    /// Initializes a new instance of the GarbageCollectionMonitor class
    /// </summary>
    /// <param name="logger">Logger instance</param>
    public GarbageCollectionMonitor(ILogger<GarbageCollectionMonitor> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public GcStatistics GetCurrentStatistics()
    {
        return new GcStatistics
        {
            Timestamp = DateTime.UtcNow,
            CollectionCounts = new Dictionary<int, long>
            {
                [0] = GC.CollectionCount(0),
                [1] = GC.CollectionCount(1),
                [2] = GC.CollectionCount(2)
            }
        };
    }

    /// <inheritdoc />
    public IObservable<GcEvent> GcEvents => _gcEventsSubject.AsObservable();

    /// <inheritdoc />
    public void StartMonitoring()
    {
        if (_isMonitoring)
            return;

        _logger.LogInformation("Starting GC monitoring");
        _isMonitoring = true;

        // In a real implementation, this would hook into GC events
    }

    /// <inheritdoc />
    public void StopMonitoring()
    {
        if (!_isMonitoring)
            return;

        _logger.LogInformation("Stopping GC monitoring");
        _isMonitoring = false;
    }

    /// <inheritdoc />
    public GcPressureAnalysis AnalyzePressure()
    {
        var stats = GetCurrentStatistics();

        return new GcPressureAnalysis
        {
            PressureLevel = GcPressureLevel.Low, // Simplified
            GcFrequency = 0.1, // Placeholder
            AveragePauseTime = TimeSpan.FromMilliseconds(1),
            TimeInGcPercent = 0.5,
            AllocationRate = 1024 * 1024, // 1MB/s placeholder
            FragmentationPercent = 5.0
        };
    }

    /// <inheritdoc />
    public IEnumerable<GcOptimizationRecommendation> GetOptimizationRecommendations()
    {
        return new List<GcOptimizationRecommendation>
        {
            new()
            {
                Category = GcOptimizationCategory.AllocationReduction,
                Priority = RecommendationPriority.Medium,
                Title = "Reduce object allocations",
                Description = "Consider using object pooling for frequently allocated objects",
                ExpectedImpact = "10-20% reduction in GC pressure"
            }
        };
    }

    /// <inheritdoc />
    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;
        _gcEventsSubject.Dispose();

        _logger.LogInformation("GC monitor disposed");
    }
}
