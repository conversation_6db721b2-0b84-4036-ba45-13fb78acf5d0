using System;
using System.IO;
using System.Threading.Tasks;
using ArtDesignFramework.TestFramework.Core;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.TestFramework
{
    /// <summary>
    /// Main program for the enterprise-grade testing framework
    /// </summary>
    public class Program
    {
        /// <summary>
        /// Main entry point for the testing framework
        /// </summary>
        /// <param name="args">Command line arguments</param>
        /// <returns>Exit code (0 for success, non-zero for failure)</returns>
        public static async Task<int> Main(string[] args)
        {
            Console.WriteLine("🏢 ArtDesignFramework - Enterprise Testing System v2.0");
            Console.WriteLine("=========================================================");
            Console.WriteLine();

            try
            {
                // Setup dependency injection
                var services = new ServiceCollection();
                ConfigureServices(services);
                var serviceProvider = services.BuildServiceProvider();

                // Parse command line arguments
                var options = ParseCommandLineArguments(args);

                if (options.ShowHelp)
                {
                    ShowHelp();
                    return 0;
                }

                // For demonstration, use the simplified test runner
                var testRunner = serviceProvider.GetRequiredService<TestRunner>();
                var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

                logger.LogInformation("🚀 Starting enterprise validation demonstration");

                // Perform validation demonstration
                var result = await testRunner.RunValidationDemoAsync();

                // Display results
                DisplayResults(result);

                // Return appropriate exit code
                return result.OverallSuccess ? 0 : 1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Fatal error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return -1;
            }
        }

        /// <summary>
        /// Configures dependency injection services
        /// </summary>
        /// <param name="services">Service collection</param>
        private static void ConfigureServices(IServiceCollection services)
        {
            // Configure logging
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // Add TestFramework with database integration
            services.AddTestFrameworkWithDatabase();
        }

        /// <summary>
        /// Parses command line arguments into test options
        /// </summary>
        /// <param name="args">Command line arguments</param>
        /// <returns>Test options</returns>
        private static EnterpriseTestOptions ParseCommandLineArguments(string[] args)
        {
            var options = new EnterpriseTestOptions();

            for (int i = 0; i < args.Length; i++)
            {
                switch (args[i].ToLowerInvariant())
                {
                    case "--help":
                    case "-h":
                        options.ShowHelp = true;
                        break;

                    case "--validate-all":
                    case "-a":
                        options.ValidateAll = true;
                        break;

                    case "--module":
                    case "-m":
                        if (i + 1 < args.Length)
                        {
                            options.SpecificModule = args[++i];
                        }
                        break;

                    case "--fix-phantom":
                    case "-f":
                        options.FixPhantomImplementations = true;
                        break;

                    case "--generate-reports":
                    case "-r":
                        options.GenerateReports = true;
                        break;

                    case "--output":
                    case "-o":
                        if (i + 1 < args.Length)
                        {
                            options.OutputDirectory = args[++i];
                        }
                        break;

                    case "--verbose":
                    case "-v":
                        options.VerboseOutput = true;
                        break;

                    case "--continuous":
                    case "-c":
                        options.ContinuousMonitoring = true;
                        break;

                    case "--enterprise-mode":
                    case "-e":
                        options.EnterpriseMode = true;
                        break;
                }
            }

            return options;
        }

        /// <summary>
        /// Shows help information
        /// </summary>
        private static void ShowHelp()
        {
            Console.WriteLine("ArtDesignFramework Enterprise Testing System v2.0");
            Console.WriteLine();
            Console.WriteLine("Usage: ArtDesignFramework.TestFramework [options]");
            Console.WriteLine();
            Console.WriteLine("Options:");
            Console.WriteLine("  -h, --help                    Show this help message");
            Console.WriteLine("  -a, --validate-all            Validate all modules comprehensively");
            Console.WriteLine("  -m, --module <name>           Validate specific module");
            Console.WriteLine("  -f, --fix-phantom             Attempt to fix phantom implementations");
            Console.WriteLine("  -r, --generate-reports        Generate comprehensive reports");
            Console.WriteLine("  -o, --output <dir>            Output directory for reports");
            Console.WriteLine("  -v, --verbose                 Verbose output");
            Console.WriteLine("  -c, --continuous              Enable continuous monitoring");
            Console.WriteLine("  -e, --enterprise-mode         Enable enterprise-grade validation");
            Console.WriteLine();
            Console.WriteLine("Examples:");
            Console.WriteLine("  # Comprehensive enterprise validation");
            Console.WriteLine("  ArtDesignFramework.TestFramework --validate-all --enterprise-mode");
            Console.WriteLine();
            Console.WriteLine("  # Validate specific module with reports");
            Console.WriteLine("  ArtDesignFramework.TestFramework -m VectorGraphics -r -v");
            Console.WriteLine();
            Console.WriteLine("  # Fix phantom implementations");
            Console.WriteLine("  ArtDesignFramework.TestFramework --fix-phantom --generate-reports");
            Console.WriteLine();
            Console.WriteLine("  # Continuous monitoring mode");
            Console.WriteLine("  ArtDesignFramework.TestFramework --continuous --enterprise-mode");
        }

        /// <summary>
        /// Displays validation results
        /// </summary>
        /// <param name="result">Enterprise validation result</param>
        private static void DisplayResults(EnterpriseValidationResult result)
        {
            Console.WriteLine();
            Console.WriteLine("📊 ENTERPRISE VALIDATION RESULTS");
            Console.WriteLine("=================================");
            Console.WriteLine();

            // Overall status
            var statusIcon = result.OverallSuccess ? "✅" : "❌";
            var statusText = result.OverallSuccess ? "PASSED" : "FAILED";
            Console.WriteLine($"Overall Status: {statusIcon} {statusText}");
            Console.WriteLine($"Validation ID: {result.ValidationId}");
            Console.WriteLine($"Duration: {result.ValidationDuration.TotalMinutes:F1} minutes");
            Console.WriteLine($"Quality Score: {result.OverallQualityScore:F1}/100");
            Console.WriteLine();

            // Module summary
            Console.WriteLine("📦 MODULE SUMMARY");
            Console.WriteLine($"Total Modules: {result.TotalModulesDiscovered}");
            Console.WriteLine($"Healthy Modules: {result.HealthyModules} ({(result.TotalModulesDiscovered > 0 ? (double)result.HealthyModules / result.TotalModulesDiscovered * 100 : 0):F1}%)");
            Console.WriteLine($"Buildable Modules: {result.BuildableModules}");
            Console.WriteLine();

            // Quality gates
            Console.WriteLine("🚪 QUALITY GATES");
            foreach (var gate in result.QualityGates)
            {
                var gateIcon = gate.Passed ? "✅" : "❌";
                Console.WriteLine($"{gateIcon} {gate.GateName}: {gate.Score:F1}/100 - {gate.Details}");
            }
            Console.WriteLine();

            // Critical issues
            if (result.HasStatusMismatches || result.HasPhantomModules || result.CriticalErrors.Any())
            {
                Console.WriteLine("⚠️  CRITICAL ISSUES");

                if (result.HasStatusMismatches)
                {
                    Console.WriteLine($"❌ Status Mismatches: {result.StatusMismatches.Count}");
                    foreach (var mismatch in result.StatusMismatches.Take(5))
                    {
                        Console.WriteLine($"   • {mismatch.ModuleName}: Claims '{mismatch.ClaimedStatus}' but is '{mismatch.ActualStatus}'");
                    }
                    if (result.StatusMismatches.Count > 5)
                    {
                        Console.WriteLine($"   ... and {result.StatusMismatches.Count - 5} more");
                    }
                }

                if (result.HasPhantomModules)
                {
                    Console.WriteLine($"👻 Phantom Modules: {result.PhantomModules.Count}");
                    foreach (var phantom in result.PhantomModules.Take(5))
                    {
                        Console.WriteLine($"   • {phantom.ModuleName}: {string.Join(", ", phantom.PhantomReasons)}");
                    }
                    if (result.PhantomModules.Count > 5)
                    {
                        Console.WriteLine($"   ... and {result.PhantomModules.Count - 5} more");
                    }
                }

                if (result.CriticalErrors.Any())
                {
                    Console.WriteLine($"🔥 Critical Errors: {result.CriticalErrors.Count}");
                    foreach (var error in result.CriticalErrors.Take(3))
                    {
                        Console.WriteLine($"   • {error}");
                    }
                    if (result.CriticalErrors.Count > 3)
                    {
                        Console.WriteLine($"   ... and {result.CriticalErrors.Count - 3} more");
                    }
                }
                Console.WriteLine();
            }

            // Recommendations
            Console.WriteLine("💡 RECOMMENDATIONS");
            if (result.HasStatusMismatches)
            {
                Console.WriteLine("1. Update module documentation to reflect actual implementation status");
            }
            if (result.HasPhantomModules)
            {
                Console.WriteLine("2. Complete implementation for phantom modules or remove false claims");
            }
            if (result.OverallQualityScore < 80)
            {
                Console.WriteLine("3. Focus on improving module health to achieve enterprise standards");
            }
            if (!result.OverallSuccess)
            {
                Console.WriteLine("4. Address critical issues before claiming production-ready status");
            }
            Console.WriteLine();

            // Report locations
            Console.WriteLine("📄 DETAILED REPORTS");
            Console.WriteLine($"Executive Summary: TestResults/ExecutiveSummary_{result.ValidationId}.md");
            Console.WriteLine($"Health Dashboard: TestResults/ModuleHealth/HealthDashboard_*.html");
            Console.WriteLine($"Build Validation: TestResults/BuildValidation/");
            Console.WriteLine();
        }
    }

    /// <summary>
    /// Enterprise test configuration options
    /// </summary>
    public class EnterpriseTestOptions
    {
        /// <summary>
        /// Gets or sets whether to show help
        /// </summary>
        public bool ShowHelp { get; set; }

        /// <summary>
        /// Gets or sets whether to validate all modules
        /// </summary>
        public bool ValidateAll { get; set; } = true;

        /// <summary>
        /// Gets or sets the specific module to validate
        /// </summary>
        public string? SpecificModule { get; set; }

        /// <summary>
        /// Gets or sets whether to fix phantom implementations
        /// </summary>
        public bool FixPhantomImplementations { get; set; }

        /// <summary>
        /// Gets or sets whether to generate reports
        /// </summary>
        public bool GenerateReports { get; set; } = true;

        /// <summary>
        /// Gets or sets the output directory for reports
        /// </summary>
        public string OutputDirectory { get; set; } = "TestResults";

        /// <summary>
        /// Gets or sets whether to use verbose output
        /// </summary>
        public bool VerboseOutput { get; set; }

        /// <summary>
        /// Gets or sets whether to enable continuous monitoring
        /// </summary>
        public bool ContinuousMonitoring { get; set; }

        /// <summary>
        /// Gets or sets whether to enable enterprise mode
        /// </summary>
        public bool EnterpriseMode { get; set; } = true;
    }
}
