# ArtDesignFramework Database Configuration Guide

**Last Updated:** 2025-06-08 21:30:00 UTC

## Overview

This guide provides comprehensive instructions for configuring and setting up the ArtDesignFramework database system. The framework uses SQLite with Entity Framework Core for cross-platform data persistence.

## Database Setup

### Automatic Database Creation
The framework automatically creates the database on first run when properly configured:

```csharp
services.AddDataAccessModule(options =>
{
    options.EnableAutomaticDatabaseCreation = true;
    options.ConnectionString = "Data Source=L:\\framework\\data\\ArtDesignFramework.db";
});
```

### Manual Database Creation
For manual setup or troubleshooting:

```bash
# Navigate to DataAccess project
cd modules/src/DataAccess

# Create database with migrations
dotnet ef database update
```

## Connection String Configuration

### Default Configuration
```csharp
public class DataAccessOptions
{
    public string ConnectionString { get; set; } = "Data Source=ArtDesignFramework.db";
    public bool EnableAutomaticDatabaseCreation { get; set; } = true;
    public bool EnablePerformanceMonitoring { get; set; } = true;
    public int DataRetentionDays { get; set; } = 30;
    public bool EnableDetailedLogging { get; set; } = false;
}
```

### Environment-Specific Configuration

#### Development Environment
```csharp
services.AddDataAccessModule(options =>
{
    options.ConnectionString = "Data Source=L:\\framework\\data\\ArtDesignFramework_Dev.db";
    options.EnableDetailedLogging = true;
    options.DataRetentionDays = 7; // Shorter retention for development
});
```

#### Production Environment
```csharp
services.AddDataAccessModule(options =>
{
    options.ConnectionString = "Data Source=L:\\framework\\data\\ArtDesignFramework.db";
    options.EnablePerformanceMonitoring = true;
    options.DataRetentionDays = 90; // Longer retention for production
    options.EnableDetailedLogging = false; // Reduce logging overhead
});
```

#### Testing Environment
```csharp
services.AddDataAccessModule(options =>
{
    options.ConnectionString = "Data Source=:memory:"; // In-memory database for tests
    options.EnableAutomaticDatabaseCreation = true;
    options.DataRetentionDays = 1; // Minimal retention for tests
});
```

## Service Registration Options

### Basic Registration
```csharp
// Minimal configuration with defaults
services.AddDataAccessModule();
```

### Advanced Registration
```csharp
services.AddDataAccessModule(options =>
{
    // Database location
    options.ConnectionString = "Data Source=L:\\framework\\data\\ArtDesignFramework.db";
    
    // Automatic setup
    options.EnableAutomaticDatabaseCreation = true;
    
    // Performance monitoring
    options.EnablePerformanceMonitoring = true;
    
    // Data retention policy
    options.DataRetentionDays = 30;
    
    // Logging configuration
    options.EnableDetailedLogging = false;
});
```

### Module Integration Configurations

#### TestFramework with Database
```csharp
// Database-enabled TestFramework
services.AddTestFrameworkWithDatabase(testOptions =>
{
    testOptions.EnableDatabaseStorage = true;
    testOptions.EnableFileBasedReports = true; // Hybrid mode
});
```

#### Performance Module with Database
```csharp
// Database-enabled Performance monitoring
services.AddPerformanceWithDatabase(perfOptions =>
{
    perfOptions.EnableDatabaseStorage = true;
    perfOptions.DatabaseStorageInterval = TimeSpan.FromSeconds(30);
});
```

## Connection String Formats

### Local File Database
```
Data Source=L:\framework\data\ArtDesignFramework.db
```

### Relative Path Database
```
Data Source=./data/ArtDesignFramework.db
```

### In-Memory Database (Testing)
```
Data Source=:memory:
```

### Advanced SQLite Options
```
Data Source=L:\framework\data\ArtDesignFramework.db;Cache=Shared;Journal Mode=WAL;Synchronous=Normal
```

## Configuration Validation

### Health Checks
```csharp
services.AddHealthChecks()
    .AddDbContextCheck<ArtDesignFrameworkDbContext>("database");
```

### Connection Testing
```csharp
public async Task<bool> TestDatabaseConnection()
{
    try
    {
        using var context = new ArtDesignFrameworkDbContext(options);
        return await context.Database.CanConnectAsync();
    }
    catch
    {
        return false;
    }
}
```

## Migration Management

### Applying Migrations
```bash
# Apply all pending migrations
dotnet ef database update --project modules/src/DataAccess

# Apply specific migration
dotnet ef database update [MigrationName] --project modules/src/DataAccess
```

### Creating New Migrations
```bash
# Create new migration
dotnet ef migrations add [MigrationName] --project modules/src/DataAccess

# Create migration with specific output directory
dotnet ef migrations add [MigrationName] --project modules/src/DataAccess --output-dir Migrations
```

### Migration Rollback
```bash
# Rollback to previous migration
dotnet ef database update [PreviousMigrationName] --project modules/src/DataAccess

# Rollback to initial state
dotnet ef database update 0 --project modules/src/DataAccess
```

## Performance Optimization

### Connection Pooling
Entity Framework Core automatically handles connection pooling for SQLite. Configure pool size if needed:

```csharp
services.AddDbContextPool<ArtDesignFrameworkDbContext>(options =>
    options.UseSqlite(connectionString), poolSize: 128);
```

### Query Optimization
```csharp
// Enable sensitive data logging in development
services.AddDbContext<ArtDesignFrameworkDbContext>(options =>
{
    options.UseSqlite(connectionString);
    if (environment.IsDevelopment())
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
});
```

### Batch Operations
```csharp
// Efficient bulk operations
await context.BulkInsertAsync(entities);
await context.BulkUpdateAsync(entities);
await context.BulkDeleteAsync(entities);
```

## Security Configuration

### Connection Security
```csharp
// Secure connection string management
var connectionString = configuration.GetConnectionString("DefaultConnection");
if (string.IsNullOrEmpty(connectionString))
{
    throw new InvalidOperationException("Database connection string not configured");
}
```

### Data Protection
```csharp
services.AddDataProtection()
    .PersistKeysToDbContext<ArtDesignFrameworkDbContext>();
```

## Backup and Recovery

### Database Backup
```bash
# SQLite database backup
sqlite3 ArtDesignFramework.db ".backup backup_$(date +%Y%m%d_%H%M%S).db"
```

### Automated Backup Script
```csharp
public async Task BackupDatabase(string backupPath)
{
    var sourceConnection = new SqliteConnection(connectionString);
    var backupConnection = new SqliteConnection($"Data Source={backupPath}");
    
    sourceConnection.Open();
    backupConnection.Open();
    
    sourceConnection.BackupDatabase(backupConnection);
}
```

## Troubleshooting

### Common Issues

#### Database Lock Issues
```csharp
// Configure WAL mode to reduce locking
services.AddDbContext<ArtDesignFrameworkDbContext>(options =>
    options.UseSqlite(connectionString, sqliteOptions =>
        sqliteOptions.CommandTimeout(30)));
```

#### Migration Conflicts
```bash
# Reset migrations (development only)
dotnet ef database drop --project modules/src/DataAccess
dotnet ef database update --project modules/src/DataAccess
```

#### Performance Issues
```csharp
// Enable query logging for diagnosis
services.AddDbContext<ArtDesignFrameworkDbContext>(options =>
    options.UseSqlite(connectionString)
           .LogTo(Console.WriteLine, LogLevel.Information));
```

### Diagnostic Commands
```bash
# Check database schema
sqlite3 ArtDesignFramework.db ".schema"

# Analyze database size
sqlite3 ArtDesignFramework.db "SELECT name, COUNT(*) FROM sqlite_master WHERE type='table' GROUP BY name;"

# Check indexes
sqlite3 ArtDesignFramework.db ".indexes"
```

## Environment Variables

### Configuration via Environment
```bash
# Set connection string via environment
export ARTDESIGNFRAMEWORK_CONNECTION_STRING="Data Source=/path/to/database.db"

# Set retention policy
export ARTDESIGNFRAMEWORK_DATA_RETENTION_DAYS=60
```

### Docker Configuration
```dockerfile
ENV ARTDESIGNFRAMEWORK_CONNECTION_STRING="Data Source=/app/data/ArtDesignFramework.db"
ENV ARTDESIGNFRAMEWORK_DATA_RETENTION_DAYS=30
```

---

**Configuration Version:** 1.0  
**Entity Framework Version:** 9.0.0  
**SQLite Version:** 3.x  
**Supported Platforms:** Windows, macOS, Linux
