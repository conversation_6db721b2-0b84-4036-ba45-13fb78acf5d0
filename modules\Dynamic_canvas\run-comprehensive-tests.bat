@echo off
echo ========================================
echo 🎨 Dynamic Canvas Comprehensive Testing
echo ========================================
echo.

echo 📦 Installing dependencies...
call npm install

echo.
echo 🧪 Running comprehensive functional tests...
call npm run test:comprehensive

echo.
echo 📊 Running automated test suite with reporting...
call npm run test:automated

echo.
echo 📈 Generating coverage report...
call npm run test:coverage

echo.
echo ✅ Testing complete! Check the test-reports directory for detailed results.
pause
