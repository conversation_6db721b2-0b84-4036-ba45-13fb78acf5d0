// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Entities;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository interface for test execution result data access
/// </summary>
public interface ITestResultRepository : IBaseRepository<TestExecutionResult>
{
    /// <summary>
    /// Gets test results by test suite name
    /// </summary>
    /// <param name="testSuite">Test suite name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetByTestSuiteAsync(string testSuite, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test results by test name
    /// </summary>
    /// <param name="testName">Test name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetByTestNameAsync(string testName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test results by session ID
    /// </summary>
    /// <param name="sessionId">Session identifier</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetBySessionAsync(Guid sessionId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test results by pass/fail status
    /// </summary>
    /// <param name="passed">Whether the test passed</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetByStatusAsync(bool passed, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test results within a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test results by environment
    /// </summary>
    /// <param name="environment">Environment name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetByEnvironmentAsync(string environment, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test results by category
    /// </summary>
    /// <param name="category">Test category</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetByCategoryAsync(string category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets failed test results with error details
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of failed test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetFailedTestsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test results with execution time above threshold
    /// </summary>
    /// <param name="thresholdMs">Execution time threshold in milliseconds</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of slow test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetSlowTestsAsync(double thresholdMs, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test execution statistics for a test suite
    /// </summary>
    /// <param name="testSuite">Test suite name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Test execution statistics</returns>
    Task<TestExecutionStatistics> GetTestSuiteStatisticsAsync(string testSuite, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test execution statistics for a date range
    /// </summary>
    /// <param name="startDate">Start date</param>
    /// <param name="endDate">End date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Test execution statistics</returns>
    Task<TestExecutionStatistics> GetStatisticsByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets recent test results for a specific test
    /// </summary>
    /// <param name="testSuite">Test suite name</param>
    /// <param name="testName">Test name</param>
    /// <param name="count">Number of recent results to retrieve</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of recent test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetRecentTestResultsAsync(string testSuite, string testName, int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test results for regression analysis
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of regression test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetRegressionTestsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets test results from automated builds
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of automated build test results</returns>
    Task<IEnumerable<TestExecutionResult>> GetAutomatedBuildTestsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Deletes old test results based on retention policy
    /// </summary>
    /// <param name="retentionDays">Number of days to retain</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of deleted records</returns>
    Task<int> CleanupOldResultsAsync(int retentionDays, CancellationToken cancellationToken = default);
}

/// <summary>
/// Test execution statistics
/// </summary>
public class TestExecutionStatistics
{
    /// <summary>
    /// Gets or sets the total number of tests
    /// </summary>
    public int TotalTests { get; set; }

    /// <summary>
    /// Gets or sets the number of passed tests
    /// </summary>
    public int PassedTests { get; set; }

    /// <summary>
    /// Gets or sets the number of failed tests
    /// </summary>
    public int FailedTests { get; set; }

    /// <summary>
    /// Gets the pass rate as a percentage
    /// </summary>
    public double PassRate => TotalTests > 0 ? (double)PassedTests / TotalTests * 100 : 0;

    /// <summary>
    /// Gets the fail rate as a percentage
    /// </summary>
    public double FailRate => TotalTests > 0 ? (double)FailedTests / TotalTests * 100 : 0;

    /// <summary>
    /// Gets or sets the average execution time in milliseconds
    /// </summary>
    public double AverageExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the total execution time in milliseconds
    /// </summary>
    public double TotalExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the minimum execution time in milliseconds
    /// </summary>
    public double MinExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the maximum execution time in milliseconds
    /// </summary>
    public double MaxExecutionTimeMs { get; set; }

    /// <summary>
    /// Gets or sets the average memory usage in bytes
    /// </summary>
    public long AverageMemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the peak memory usage in bytes
    /// </summary>
    public long PeakMemoryUsageBytes { get; set; }

    /// <summary>
    /// Gets or sets the average CPU usage percentage
    /// </summary>
    public double AverageCpuUsagePercent { get; set; }
}
