# Selection Tools Guide

**Generated:** 2025-01-09 - Advanced Canvas Selection Tools Documentation
**Last Updated:** 2025-01-09 23:59:00 UTC
**Document Type:** User Guide
**Status:** Active
**Document Version:** 1.0

---

## Executive Summary

This guide provides comprehensive documentation for the advanced canvas selection tools in ArtDesignFramework, including rectangle, ellipse, lasso, magic wand, and eye dropper functionality. The selection tools are integrated with the existing ColorPickerControl and SkiaSharpCanvasRenderer for optimal performance and user experience.

**Last Updated:** 2025-01-09 23:59:00 UTC

---

## Selection Tools Overview

### Available Selection Tools
**Last Updated:** 2025-01-09 23:59:00 UTC

The ArtDesignFramework provides five primary selection tools:

1. **Rectangle Selection** - Precise rectangular area selection
2. **Ellipse Selection** - Circular and elliptical area selection  
3. **Lasso Selection** - Freehand selection with custom paths
4. **Magic Wand Selection** - Color-based intelligent selection
5. **Eye Dropper** - Color sampling and selection tool

### Performance Targets
- **Rectangle/Ellipse Selection**: < 100ms response time
- **Lasso Selection**: < 200ms response time
- **Magic Wand Selection**: < 500ms response time
- **Eye Dropper**: < 50ms response time

## Rectangle Selection Tool

### Overview
**Last Updated:** 2025-01-09 23:59:00 UTC

The rectangle selection tool provides precise rectangular area selection with real-time visual feedback and accurate bounds calculation.

### Usage

#### Basic Rectangle Selection
```csharp
// Initialize selection tools engine
var selectionEngine = serviceProvider.GetRequiredService<SelectionToolsEngine>();
await selectionEngine.InitializeAsync();

// Start rectangle selection
await selectionEngine.StartRectangleSelectionAsync(startX, startY);

// Update selection bounds (typically in mouse move events)
await selectionEngine.UpdateRectangleSelectionAsync(currentX, currentY);

// Complete selection
var selection = await selectionEngine.EndRectangleSelectionAsync();

if (selection != null)
{
    var bounds = selection.Bounds;
    Console.WriteLine($"Selected area: {bounds.Width}x{bounds.Height} at ({bounds.X}, {bounds.Y})");
}
```

#### Advanced Features
- **Aspect Ratio Constraint**: Hold Shift key for square selection
- **Center-Based Selection**: Hold Alt key for center-based selection
- **Real-Time Preview**: Visual feedback during selection process

## Ellipse Selection Tool

### Overview
**Last Updated:** 2025-01-09 23:59:00 UTC

The ellipse selection tool enables circular and elliptical area selection with similar workflow to rectangle selection.

### Usage

#### Basic Ellipse Selection
```csharp
// Start ellipse selection
await selectionEngine.StartEllipseSelectionAsync(startX, startY);

// Update selection (creates ellipse from start point to current point)
await selectionEngine.UpdateEllipseSelectionAsync(currentX, currentY);

// Complete selection
var selection = await selectionEngine.EndEllipseSelectionAsync();
```

#### Mathematical Properties
- **Ellipse Calculation**: Uses standard ellipse equation for precise bounds
- **Center Point**: Automatically calculated from start and end points
- **Radius Calculation**: Supports both horizontal and vertical radius

## Lasso Selection Tool

### Overview
**Last Updated:** 2025-01-09 23:59:00 UTC

The lasso selection tool provides freehand selection capabilities with custom path definition and automatic path closure.

### Usage

#### Freehand Lasso Selection
```csharp
// Start lasso selection
await selectionEngine.StartLassoSelectionAsync(startX, startY);

// Add points to the lasso path (typically in mouse move events)
await selectionEngine.UpdateLassoSelectionAsync(pointX, pointY);

// Complete selection (automatically closes path)
var selection = await selectionEngine.EndLassoSelectionAsync();
```

#### Path Management
- **Point Collection**: Efficient storage of path points
- **Path Simplification**: Automatic reduction of redundant points
- **Bounds Calculation**: Dynamic bounding box calculation
- **Path Closure**: Automatic path closure for complete selection

### Performance Optimization
- **Point Limiting**: Maximum 10,000 points per lasso selection
- **Simplification Algorithm**: Douglas-Peucker algorithm for path optimization
- **Memory Management**: Efficient point collection with automatic cleanup

## Magic Wand Selection Tool

### Overview
**Last Updated:** 2025-01-09 23:59:00 UTC

The magic wand selection tool provides intelligent color-based selection using advanced flood fill algorithms with configurable tolerance and contiguity options.

### Usage

#### Basic Magic Wand Selection
```csharp
// Magic wand selection with tolerance and contiguity
var tolerance = 0.1; // 10% color tolerance
var contiguous = true; // Only select connected pixels

var selection = await selectionEngine.MagicWandSelectionAsync(
    clickX, clickY, tolerance, contiguous);

if (selection != null)
{
    Console.WriteLine($"Selected {selection.PixelCount} pixels");
}
```

#### Advanced Configuration
```csharp
// Advanced magic wand options
var options = new MagicWandOptions
{
    Tolerance = 0.15,
    Contiguous = false, // Select all similar colors regardless of connection
    SampleMerged = true, // Sample from all visible layers
    AntiAlias = true, // Smooth selection edges
    FeatherRadius = 2.0 // Soft selection edges
};

var selection = await selectionEngine.MagicWandSelectionAsync(clickX, clickY, options);
```

### Algorithm Details
- **Flood Fill Implementation**: Queue-based flood fill for optimal performance
- **Color Matching**: RGBA channel difference calculation with configurable tolerance
- **Contiguity Options**: Support for both contiguous and non-contiguous selection
- **Performance**: Optimized for large images with early termination conditions

## Eye Dropper Tool

### Overview
**Last Updated:** 2025-01-09 23:59:00 UTC

The eye dropper tool provides color sampling functionality with integration to ColorPickerControl for seamless color selection workflow.

### Usage

#### Basic Color Sampling
```csharp
// Sample color at specific coordinates
var sampledColor = await selectionEngine.SampleColorAsync(x, y);

if (sampledColor.HasValue)
{
    var color = sampledColor.Value;
    Console.WriteLine($"Sampled color: R={color.Red}, G={color.Green}, B={color.Blue}, A={color.Alpha}");
    
    // Apply to color picker
    colorPicker.SetSelectedColor(color);
}
```

#### Integration with ColorPickerControl
```csharp
// Automatic color picker integration
var colorPicker = serviceProvider.GetRequiredService<ColorPickerControl>();

// Sample and apply color in one operation
await selectionEngine.SampleAndApplyColorAsync(x, y, colorPicker);
```

### Sampling Features
- **Pixel-Perfect Sampling**: Exact color value at specified coordinates
- **Average Sampling**: Sample average color from small area around point
- **Layer Sampling**: Sample from specific layer or merged result
- **Format Support**: Support for various color formats (RGB, RGBA, HSV, HSL)

## Selection Management

### Selection History
**Last Updated:** 2025-01-09 23:59:00 UTC

The selection tools engine maintains a comprehensive selection history with undo/redo capabilities.

#### History Operations
```csharp
// Undo last selection
var previousSelection = selectionEngine.UndoSelection();

// Redo selection
var redoneSelection = selectionEngine.RedoSelection();

// Clear selection history
selectionEngine.ClearSelectionHistory();

// Get selection statistics
var stats = selectionEngine.GetSelectionStatistics();
Console.WriteLine($"Total selections: {stats.TotalSelections}");
Console.WriteLine($"History size: {stats.HistorySize}");
```

#### History Configuration
- **Maximum History Size**: 50 selections (configurable)
- **Memory Management**: Automatic cleanup of old selections
- **Persistence**: Optional selection history persistence

### Selection Operations

#### Selection Modification
```csharp
// Invert current selection
selectionEngine.InvertSelection();

// Clear current selection
selectionEngine.ClearSelection();

// Combine selections
var combinedSelection = selectionEngine.CombineSelections(
    selection1, selection2, CombineMode.Union);
```

#### Selection Export
```csharp
// Export selection as mask
var maskBitmap = selectionEngine.ExportSelectionAsMask(selection);

// Export selection bounds
var bounds = selection.Bounds;
var exportData = new SelectionExportData
{
    Bounds = bounds,
    PixelCount = selection.PixelCount,
    SelectionType = selection.Type
};
```

## Integration Examples

### Canvas Integration
**Last Updated:** 2025-01-09 23:59:00 UTC

#### SkiaSharpCanvasRenderer Integration
```csharp
// Configure selection tools with canvas renderer
selectionEngine.SetCanvasRenderer(canvasRenderer);
selectionEngine.SetCanvasSize(canvasWidth, canvasHeight);

// Handle selection changes
selectionEngine.SelectionChanged += (sender, args) =>
{
    if (args.Selection != null)
    {
        // Update canvas with selection overlay
        canvasRenderer.InvalidateRegion(args.Selection.Bounds);
    }
};
```

### Clock Application Integration
```csharp
// Clock workshop integration example
public class ClockWorkshopViewModel
{
    private readonly SelectionToolsEngine _selectionEngine;
    
    public async Task ActivateSelectionTool(SelectionType toolType)
    {
        _selectionEngine.SetActiveSelectionTool(toolType);
        
        // Update UI to reflect active tool
        ActiveSelectionTool = toolType;
        SelectionToolsEnabled = true;
    }
    
    private void OnSelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (e.Selection != null)
        {
            // Update text positioning based on selection
            UpdateTextPositionFromSelection(e.Selection);
            PreviewUpdateRequested?.Invoke();
        }
    }
}
```

## Performance Optimization

### Best Practices
**Last Updated:** 2025-01-09 23:59:00 UTC

1. **Initialize Once**: Initialize SelectionToolsEngine once per application session
2. **Dispose Properly**: Always dispose of selection resources when done
3. **Limit History Size**: Configure appropriate selection history limits
4. **Optimize Tolerance**: Use appropriate tolerance values for magic wand selection
5. **Batch Operations**: Group multiple selection operations when possible

### Memory Management
- **Automatic Cleanup**: Selection engine automatically manages memory for selections
- **Resource Pooling**: Internal pooling of selection-related objects
- **Garbage Collection**: Minimal GC pressure through efficient object reuse

## Troubleshooting

### Common Issues
**Last Updated:** 2025-01-09 23:59:00 UTC

#### Selection Not Working
- **Check Initialization**: Ensure SelectionToolsEngine is properly initialized
- **Verify Canvas Size**: Confirm canvas size is set correctly
- **Check Coordinates**: Verify selection coordinates are within canvas bounds

#### Performance Issues
- **Reduce Tolerance**: Lower magic wand tolerance for better performance
- **Limit History**: Reduce selection history size if memory is constrained
- **Optimize Paths**: Use path simplification for complex lasso selections

#### Integration Problems
- **Event Handling**: Ensure proper event handler registration
- **Thread Safety**: Use selection tools from UI thread only
- **Resource Disposal**: Verify proper disposal of selection resources

---

**Document Status:** Active User Guide
**Next Review:** 2025-02-09 23:59:00 UTC
**Responsible:** UI/UX Engineering Team
**Approval:** Product Management Approved

---

*This document reflects the current selection tools capabilities as of 2025-01-09 23:59:00 UTC, and will be updated as new features are implemented.*
