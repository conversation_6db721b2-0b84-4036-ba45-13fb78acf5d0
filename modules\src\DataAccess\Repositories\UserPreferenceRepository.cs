// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using ArtDesignFramework.Core;
using ArtDesignFramework.DataAccess.Configuration;
using ArtDesignFramework.DataAccess.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace ArtDesignFramework.DataAccess.Repositories;

/// <summary>
/// Repository implementation for user preference data access
/// </summary>
public class UserPreferenceRepository : BaseRepository<UserPreference>, IUserPreferenceRepository
{
    public UserPreferenceRepository(ArtDesignFrameworkDbContext context, ILogger<UserPreferenceRepository> logger)
        : base(context, logger) { }

    public async Task<IEnumerable<UserPreference>> GetByUserAsync(string userId, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.IsActive).OrderBy(p => p.PreferenceKey).ToListAsync(cancellationToken);

    public async Task<UserPreference?> GetByUserAndKeyAsync(string userId, string key, CancellationToken cancellationToken = default)
        => await DbSet.FirstOrDefaultAsync(p => p.UserId == userId && p.PreferenceKey == key, cancellationToken);

    public async Task<string?> GetValueAsync(string userId, string key, CancellationToken cancellationToken = default)
    {
        var preference = await GetByUserAndKeyAsync(userId, key, cancellationToken);
        return preference?.PreferenceValue;
    }

    public async Task<string> GetValueOrDefaultAsync(string userId, string key, string defaultValue, CancellationToken cancellationToken = default)
    {
        var value = await GetValueAsync(userId, key, cancellationToken);
        return value ?? defaultValue;
    }

    public async Task<IEnumerable<UserPreference>> GetByApplicationAsync(string userId, string application, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.Application == application && p.IsActive).OrderBy(p => p.PreferenceKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<UserPreference>> GetByCategoryAsync(string userId, string category, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.Category == category && p.IsActive).OrderBy(p => p.PreferenceKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<UserPreference>> GetByModuleAsync(string userId, string moduleName, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.ModuleName == moduleName && p.IsActive).OrderBy(p => p.PreferenceKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<UserPreference>> GetByComponentAsync(string userId, string componentName, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.ComponentName == componentName && p.IsActive).OrderBy(p => p.PreferenceKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<UserPreference>> GetSynchronizedPreferencesAsync(string userId, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.IsSynchronized && p.IsActive).OrderBy(p => p.PreferenceKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<UserPreference>> GetTemporaryPreferencesAsync(string userId, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.IsTemporary && p.IsActive).OrderBy(p => p.PreferenceKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<UserPreference>> GetActivePreferencesAsync(string userId, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.IsActive && (p.ExpiresAt == null || p.ExpiresAt > DateTime.UtcNow)).OrderBy(p => p.PreferenceKey).ToListAsync(cancellationToken);

    public async Task<IEnumerable<UserPreference>> GetExpiredPreferencesAsync(string userId, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.ExpiresAt != null && p.ExpiresAt <= DateTime.UtcNow).OrderBy(p => p.PreferenceKey).ToListAsync(cancellationToken);

    public async Task<UserPreference> SetValueAsync(string userId, string key, string value, CancellationToken cancellationToken = default)
    {
        var preference = await GetByUserAndKeyAsync(userId, key, cancellationToken);
        if (preference != null)
        {
            preference.PreferenceValue = value;
            preference.UpdatedAt = DateTime.UtcNow;
            preference.AccessCount++;
            preference.LastAccessedAt = DateTime.UtcNow;
            return await UpdateAsync(preference, cancellationToken);
        }

        var newPreference = new UserPreference
        {
            UserId = userId,
            PreferenceKey = key,
            PreferenceValue = value,
            DataType = "String",
            Category = "General",
            Application = "ArtDesignFramework",
            Scope = "User"
        };
        return await AddAsync(newPreference, cancellationToken);
    }

    public async Task SetValuesAsync(string userId, Dictionary<string, string> preferences, CancellationToken cancellationToken = default)
    {
        foreach (var kvp in preferences)
        {
            await SetValueAsync(userId, kvp.Key, kvp.Value, cancellationToken);
        }
    }

    public async Task<bool> DeletePreferenceAsync(string userId, string key, CancellationToken cancellationToken = default)
    {
        var preference = await GetByUserAndKeyAsync(userId, key, cancellationToken);
        if (preference != null)
        {
            await DeleteAsync(preference, cancellationToken);
            return true;
        }
        return false;
    }

    public async Task<int> DeleteAllUserPreferencesAsync(string userId, CancellationToken cancellationToken = default)
    {
        var preferences = await DbSet.Where(p => p.UserId == userId).ToListAsync(cancellationToken);
        if (preferences.Any())
        {
            await DeleteRangeAsync(preferences, cancellationToken);
        }
        return preferences.Count;
    }

    public async Task UpdateAccessCountAsync(string userId, string key, CancellationToken cancellationToken = default)
    {
        var preference = await GetByUserAndKeyAsync(userId, key, cancellationToken);
        if (preference != null)
        {
            preference.AccessCount++;
            preference.LastAccessedAt = DateTime.UtcNow;
            await UpdateAsync(preference, cancellationToken);
        }
    }

    public async Task<UserPreferenceStatistics> GetUserStatisticsAsync(string userId, CancellationToken cancellationToken = default)
    {
        var preferences = await DbSet.Where(p => p.UserId == userId).ToListAsync(cancellationToken);
        var mostAccessed = preferences.OrderByDescending(p => p.AccessCount).FirstOrDefault();

        return new UserPreferenceStatistics
        {
            UserId = userId,
            TotalPreferences = preferences.Count,
            ActivePreferences = preferences.Count(p => p.IsActive),
            SynchronizedPreferences = preferences.Count(p => p.IsSynchronized),
            TemporaryPreferences = preferences.Count(p => p.IsTemporary),
            TotalAccessCount = preferences.Sum(p => p.AccessCount),
            AverageAccessCount = preferences.Any() ? preferences.Average(p => p.AccessCount) : 0,
            MostAccessedKey = mostAccessed?.PreferenceKey,
            MostAccessedCount = mostAccessed?.AccessCount ?? 0,
            LastAccessTime = preferences.Max(p => p.LastAccessedAt),
            ApplicationCount = preferences.Select(p => p.Application).Distinct().Count(),
            CategoryCount = preferences.Select(p => p.Category).Distinct().Count()
        };
    }

    public async Task<IEnumerable<UserPreference>> GetMostAccessedPreferencesAsync(string userId, int count = 10, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId).OrderByDescending(p => p.AccessCount).Take(count).ToListAsync(cancellationToken);

    public async Task<Dictionary<string, string>> ExportPreferencesAsync(string userId, string? application = null, string? category = null, CancellationToken cancellationToken = default)
    {
        var query = DbSet.Where(p => p.UserId == userId && p.IsActive);

        if (!string.IsNullOrEmpty(application))
            query = query.Where(p => p.Application == application);

        if (!string.IsNullOrEmpty(category))
            query = query.Where(p => p.Category == category);

        var preferences = await query.ToListAsync(cancellationToken);
        return preferences.ToDictionary(p => p.PreferenceKey, p => p.PreferenceValue ?? string.Empty);
    }

    public async Task<int> ImportPreferencesAsync(string userId, Dictionary<string, string> preferences, string application, bool overwriteExisting = false, CancellationToken cancellationToken = default)
    {
        int imported = 0;
        foreach (var kvp in preferences)
        {
            var existing = await GetByUserAndKeyAsync(userId, kvp.Key, cancellationToken);
            if (existing == null || overwriteExisting)
            {
                await SetValueAsync(userId, kvp.Key, kvp.Value, cancellationToken);
                imported++;
            }
        }
        return imported;
    }

    public async Task<IEnumerable<UserPreference>> SynchronizePreferencesAsync(string userId, string deviceId, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.UserId == userId && p.IsSynchronized && p.IsActive).ToListAsync(cancellationToken);

    public async Task<int> CleanupExpiredPreferencesAsync(CancellationToken cancellationToken = default)
    {
        var expiredPreferences = await DbSet.Where(p => p.ExpiresAt != null && p.ExpiresAt <= DateTime.UtcNow).ToListAsync(cancellationToken);
        if (expiredPreferences.Any())
        {
            await DeleteRangeAsync(expiredPreferences, cancellationToken);
        }
        return expiredPreferences.Count;
    }

    public async Task<IEnumerable<string>> GetUsersWithPreferencesAsync(string application, CancellationToken cancellationToken = default)
        => await DbSet.Where(p => p.Application == application).Select(p => p.UserId).Distinct().ToListAsync(cancellationToken);
}
