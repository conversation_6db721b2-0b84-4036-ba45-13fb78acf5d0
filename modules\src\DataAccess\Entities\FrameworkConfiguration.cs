// Copyright © ArtDesignFramework 2025
// Licensed under the MIT License

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArtDesignFramework.DataAccess.Entities;

/// <summary>
/// Entity representing framework configuration settings and feature flags
/// </summary>
[Table("FrameworkConfigurations")]
public class FrameworkConfiguration
{
    /// <summary>
    /// Gets or sets the unique identifier for the configuration entry
    /// </summary>
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Gets or sets the configuration key (unique identifier for the setting)
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string ConfigurationKey { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the configuration value
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? ConfigurationValue { get; set; }

    /// <summary>
    /// Gets or sets the data type of the configuration value
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string DataType { get; set; } = "String";

    /// <summary>
    /// Gets or sets the default value for this configuration
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? DefaultValue { get; set; }

    /// <summary>
    /// Gets or sets the configuration category for organization
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Category { get; set; } = "General";

    /// <summary>
    /// Gets or sets the subcategory for more granular organization
    /// </summary>
    [MaxLength(100)]
    public string? Subcategory { get; set; }

    /// <summary>
    /// Gets or sets the configuration description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets whether this configuration is a feature flag
    /// </summary>
    public bool IsFeatureFlag { get; set; }

    /// <summary>
    /// Gets or sets whether this configuration is environment-specific
    /// </summary>
    public bool IsEnvironmentSpecific { get; set; }

    /// <summary>
    /// Gets or sets whether this configuration is user-specific
    /// </summary>
    public bool IsUserSpecific { get; set; }

    /// <summary>
    /// Gets or sets whether this configuration is machine-specific
    /// </summary>
    public bool IsMachineSpecific { get; set; }

    /// <summary>
    /// Gets or sets whether this configuration requires restart to take effect
    /// </summary>
    public bool RequiresRestart { get; set; }

    /// <summary>
    /// Gets or sets whether this configuration is read-only
    /// </summary>
    public bool IsReadOnly { get; set; }

    /// <summary>
    /// Gets or sets whether this configuration is encrypted
    /// </summary>
    public bool IsEncrypted { get; set; }

    /// <summary>
    /// Gets or sets whether this configuration is sensitive (should not be logged)
    /// </summary>
    public bool IsSensitive { get; set; }

    /// <summary>
    /// Gets or sets the minimum allowed value (for numeric types)
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? MinValue { get; set; }

    /// <summary>
    /// Gets or sets the maximum allowed value (for numeric types)
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? MaxValue { get; set; }

    /// <summary>
    /// Gets or sets the allowed values as JSON array (for enum-like types)
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? AllowedValuesJson { get; set; }

    /// <summary>
    /// Gets or sets the validation pattern (regex) for string values
    /// </summary>
    [MaxLength(500)]
    public string? ValidationPattern { get; set; }

    /// <summary>
    /// Gets or sets the validation error message
    /// </summary>
    [MaxLength(500)]
    public string? ValidationErrorMessage { get; set; }

    /// <summary>
    /// Gets or sets the configuration priority (higher values take precedence)
    /// </summary>
    public int Priority { get; set; } = 0;

    /// <summary>
    /// Gets or sets the configuration scope (Global, Module, User, etc.)
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Scope { get; set; } = "Global";

    /// <summary>
    /// Gets or sets the module name if this is module-specific configuration
    /// </summary>
    [MaxLength(200)]
    public string? ModuleName { get; set; }

    /// <summary>
    /// Gets or sets the user identifier if this is user-specific configuration
    /// </summary>
    [MaxLength(100)]
    public string? UserId { get; set; }

    /// <summary>
    /// Gets or sets the machine name if this is machine-specific configuration
    /// </summary>
    [MaxLength(100)]
    public string? MachineName { get; set; }

    /// <summary>
    /// Gets or sets the environment where this configuration applies
    /// </summary>
    [MaxLength(100)]
    public string Environment { get; set; } = "All";

    /// <summary>
    /// Gets or sets the framework version this configuration applies to
    /// </summary>
    [MaxLength(50)]
    public string? FrameworkVersion { get; set; }

    /// <summary>
    /// Gets or sets when this configuration becomes effective
    /// </summary>
    public DateTime? EffectiveFrom { get; set; }

    /// <summary>
    /// Gets or sets when this configuration expires
    /// </summary>
    public DateTime? EffectiveTo { get; set; }

    /// <summary>
    /// Gets or sets the configuration source (File, Database, Environment, etc.)
    /// </summary>
    [MaxLength(100)]
    public string Source { get; set; } = "Database";

    /// <summary>
    /// Gets or sets the configuration metadata as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? MetadataJson { get; set; }

    /// <summary>
    /// Gets or sets the configuration change history as JSON
    /// </summary>
    [Column(TypeName = "TEXT")]
    public string? ChangeHistoryJson { get; set; }

    /// <summary>
    /// Gets or sets who created this configuration
    /// </summary>
    [MaxLength(100)]
    public string? CreatedBy { get; set; }

    /// <summary>
    /// Gets or sets who last modified this configuration
    /// </summary>
    [MaxLength(100)]
    public string? ModifiedBy { get; set; }

    /// <summary>
    /// Gets or sets when the configuration was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the configuration was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets when the configuration was last accessed
    /// </summary>
    public DateTime? LastAccessedAt { get; set; }

    /// <summary>
    /// Gets or sets how many times this configuration has been accessed
    /// </summary>
    public long AccessCount { get; set; } = 0;

    /// <summary>
    /// Gets or sets whether this configuration is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;

    /// <summary>
    /// Gets or sets tags associated with this configuration
    /// </summary>
    [MaxLength(1000)]
    public string? Tags { get; set; }

    /// <summary>
    /// Gets or sets additional notes or comments
    /// </summary>
    [MaxLength(2000)]
    public string? Notes { get; set; }
}
